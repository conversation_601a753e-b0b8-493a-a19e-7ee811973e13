package com.ljhj.app.ui.components

import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.ljhj.app.utils.*
import kotlinx.coroutines.flow.collectLatest

/**
 * 增强版主屏幕示例 - 展示如何使用新的工具类
 * 这是一个示例组件，展示了权限管理、位置服务和状态管理的集成使用
 */
@Suppress("unused") // 这是一个示例函数
@Composable
fun EnhancedMainScreenExample() {
    val context = LocalContext.current
    
    // 权限状态管理
    val permissionState = rememberPermissionState()
    var showPermissionDialog by remember { mutableStateOf(false) }
    var showPermissionDeniedDialog by remember { mutableStateOf(false) }
    
    // 位置状态管理
    val gpsStatusManager = remember { GpsStatusManager() }
    var showGpsAccuracyDialog by remember { mutableStateOf(false) }
    
    // UI状态
    var statusMessage by remember { mutableStateOf("正在初始化...") }
    var isLoading by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf<String?>(null) }
    
    // 权限请求启动器
    val permissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        permissionState.handlePermissionResult(permissions)
        
        val deniedPermissions = permissions.filter { !it.value }.keys.toList()
        if (deniedPermissions.isNotEmpty()) {
            showPermissionDeniedDialog = true
        }
    }
    
    // 位置管理器
    val locationManager = remember { LocationManager(context) }
    
    // 监听位置更新
    LaunchedEffect(permissionState.hasAllPermissions) {
        if (permissionState.hasAllPermissions) {
            statusMessage = "正在获取位置..."
            isLoading = true
            gpsStatusManager.startSearching()
            
            locationManager.getLocationUpdates().collectLatest { result ->
                when (result) {
                    is LocationManager.LocationResult.Success -> {
                        gpsStatusManager.updateLocation(result.location)
                        statusMessage = "位置获取成功"
                        isLoading = false
                        errorMessage = null
                        
                        // 这里可以继续处理位置信息，比如获取附近站点
                        Logger.location("Location obtained: ${result.location.latitude}, ${result.location.longitude}")
                    }
                    
                    is LocationManager.LocationResult.LowAccuracy -> {
                        gpsStatusManager.updateLocation(result.location)
                        if (gpsStatusManager.shouldShowAccuracyDialog()) {
                            showGpsAccuracyDialog = true
                        }
                    }
                    
                    is LocationManager.LocationResult.Error -> {
                        statusMessage = result.message
                        isLoading = false
                        errorMessage = result.message
                        gpsStatusManager.stopSearching()
                    }
                }
            }
        }
    }
    
    // 清理资源
    DisposableEffect(Unit) {
        onDispose {
            locationManager.cleanup()
            gpsStatusManager.reset()
        }
    }
    
    // 主界面
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        
        // 状态指示器
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            GpsStatusIndicator(
                isSearching = gpsStatusManager.isSearching,
                accuracy = gpsStatusManager.currentAccuracy
            )
            
            NetworkStatusIndicator(
                isConnected = true, // 这里应该从网络状态管理器获取
                isUploading = false // 这里应该从上传状态管理器获取
            )
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // 主要内容
        if (!permissionState.hasAllPermissions) {
            // 权限请求界面
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "需要权限才能使用应用",
                        style = MaterialTheme.typography.headlineSmall
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Button(
                        onClick = { showPermissionDialog = true }
                    ) {
                        Text("授予权限")
                    }
                }
            }
        } else if (isLoading) {
            // 加载状态
            LoadingIndicator(message = statusMessage)
        } else if (errorMessage != null) {
            // 错误状态
            ErrorMessage(
                message = errorMessage!!,
                onRetry = {
                    errorMessage = null
                    // 重新开始位置获取
                }
            )
        } else {
            // 正常状态
            SuccessMessage(message = statusMessage)
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 这里可以添加其他功能按钮
            Button(
                onClick = {
                    // 导航到相机界面等
                }
            ) {
                Text("开始拍摄")
            }
        }
    }
    
    // 权限请求对话框
    if (showPermissionDialog) {
        PermissionRequestDialog(
            permissions = permissionState.deniedPermissions,
            onPermissionResult = { result ->
                permissionState.handlePermissionResult(result)
                showPermissionDialog = false
            },
            onDismiss = { showPermissionDialog = false },
            permissionLauncher = permissionLauncher
        )
    }
    
    // 权限被拒绝对话框
    if (showPermissionDeniedDialog) {
        PermissionDeniedDialog(
            onOpenSettings = {
                PermissionManager.openAppSettings(context)
                showPermissionDeniedDialog = false
            },
            onDismiss = { showPermissionDeniedDialog = false }
        )
    }
    
    // GPS精度确认对话框
    if (showGpsAccuracyDialog) {
        val currentAccuracy = gpsStatusManager.currentAccuracy ?: 0f
        GpsAccuracyDialog(
            currentAccuracy = currentAccuracy,
            onAccept = {
                showGpsAccuracyDialog = false
                // 接受当前精度，继续流程
            },
            onWaitForBetter = {
                showGpsAccuracyDialog = false
                // 继续等待更好的信号
            },
            onCancel = {
                showGpsAccuracyDialog = false
                gpsStatusManager.stopSearching()
            },
            showDialog = true
        )
    }
}
