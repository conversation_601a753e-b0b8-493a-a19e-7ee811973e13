package com.ljhj.app.workers

import android.content.Context
import android.net.Uri
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.ljhj.app.data.db.AppDatabase
import com.ljhj.app.network.NetworkManager
import com.ljhj.app.utils.ErrorHandler
import com.ljhj.app.utils.Logger
import com.ljhj.app.utils.RetryExecutor
import com.ljhj.app.utils.UploadRetryPolicy
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import org.json.JSONObject
import java.io.IOException
import java.util.concurrent.TimeUnit

class UploadWorker(appContext: Context, workerParams: WorkerParameters) : CoroutineWorker(appContext, workerParams) {

    private val db = AppDatabase.getDatabase(applicationContext)
    private val uploadTaskDao = db.uploadTaskDao()

    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(60, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS)
        .build()

    override suspend fun doWork(): Result {
        val photoUriString = inputData.getString(KEY_PHOTO_URI) ?: return Result.failure()
        val originalFilename = inputData.getString(KEY_ORIGINAL_FILENAME) ?: return Result.failure()
        val token = inputData.getString(KEY_TOKEN) ?: return Result.failure()
        val companyName = inputData.getString(KEY_COMPANY_NAME) ?: ""
        val photoUri = Uri.parse(photoUriString)

        Logger.upload("Starting upload for file: $originalFilename")

        return try {
            // 更新状态为上传中
            RetryExecutor.executeWithRetry(
                retryPolicy = UploadRetryPolicy.database,
                operation = "更新上传状态"
            ) {
                uploadTaskDao.updateStatusByWorkRequestId(id, "UPLOADING")
            }

            // 执行上传
            val uploadResult = RetryExecutor.executeWithRetry(
                retryPolicy = UploadRetryPolicy.networkUpload,
                operation = "文件上传"
            ) { attempt ->
                Logger.upload("Upload attempt ${attempt + 1} for file: $originalFilename")
                uploadImage(applicationContext, photoUri, originalFilename, token, companyName)
            }

            // 处理上传结果
            handleUploadResponse(uploadResult, originalFilename)

        } catch (e: Exception) {
            Logger.e("Upload failed for file: $originalFilename", e)
            ErrorHandler.handleUploadError(applicationContext, e, originalFilename, false)

            try {
                uploadTaskDao.updateStatusByWorkRequestId(id, "FAILED")
            } catch (dbException: Exception) {
                Logger.e("Failed to update upload status to FAILED", dbException)
            }

            Result.failure()
        }
    }

    private suspend fun handleUploadResponse(response: okhttp3.Response, filename: String): Result {
        return if (response.isSuccessful) {
            val responseBody = response.body?.string()
            if (responseBody != null) {
                try {
                    val jsonObject = JSONObject(responseBody)
                    if (jsonObject.optInt("code") == 1) {
                        Logger.upload("Upload successful for file: $filename")
                        uploadTaskDao.updateStatusByWorkRequestId(id, "SUCCEEDED")
                        Result.success()
                    } else {
                        val errorMsg = jsonObject.optString("msg", "Unknown server error")
                        Logger.e("Upload failed with business logic error: $errorMsg")
                        uploadTaskDao.updateStatusByWorkRequestId(id, "FAILED")
                        Result.failure()
                    }
                } catch (e: Exception) {
                    Logger.e("Failed to parse server response for file: $filename", e)
                    uploadTaskDao.updateStatusByWorkRequestId(id, "FAILED")
                    Result.failure()
                }
            } else {
                Logger.e("Upload failed: Empty response body for file: $filename")
                uploadTaskDao.updateStatusByWorkRequestId(id, "FAILED")
                Result.failure()
            }
        } else {
            Logger.e("Upload failed with HTTP code: ${response.code}, message: ${response.message} for file: $filename")
            uploadTaskDao.updateStatusByWorkRequestId(id, "FAILED")
            Result.failure()
        }
    }

    private suspend fun uploadImage(
        context: Context,
        imageUri: Uri,
        fileName: String,
        token: String,
        companyName: String
    ): okhttp3.Response = withContext(Dispatchers.IO) {

        // 读取文件数据，带重试
        val fileBytes = RetryExecutor.executeWithRetry(
            retryPolicy = UploadRetryPolicy.fileRead,
            operation = "文件读取"
        ) {
            Logger.upload("Reading file bytes from Uri: $imageUri")
            val inputStream = context.contentResolver.openInputStream(imageUri)
                ?: throw IOException("Cannot open input stream for Uri: $imageUri")

            inputStream.use { it.readBytes() }
        }

        Logger.upload("File bytes read successfully, size: ${fileBytes.size} bytes")

        val requestBody = MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart(
                "file",
                fileName,
                RequestBody.create("image/webp".toMediaTypeOrNull(), fileBytes)
            )
            .addFormDataPart("companyName", companyName)
            .build()

        val uploadUrl = NetworkManager.buildUrl(NetworkManager.ApiConfig.UPLOAD)
        val request = NetworkManager.createRequestBuilder(uploadUrl, token)
            .post(requestBody)
            .build()

        Logger.network("Executing upload request", request.url.toString())
        val client = NetworkManager.createUploadClient()
        client.newCall(request).execute()
    }

    companion object {
        const val KEY_PHOTO_URI = "PHOTO_URI"
        const val KEY_ORIGINAL_FILENAME = "ORIGINAL_FILENAME"
        const val KEY_TOKEN = "TOKEN"
        const val KEY_COMPANY_NAME = "COMPANY_NAME"
    }
}
