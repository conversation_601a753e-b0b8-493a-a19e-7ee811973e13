<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\exception\UploadException;
use app\common\library\Upload;
use app\common\model\Area;
use app\common\model\Version;
use fast\Random;
use think\captcha\Captcha;
use think\Config;
use think\Hook;

/**
 * 公共接口
 */
class Common extends Api
{
    protected $noNeedLogin = ['init', 'captcha'];
    protected $noNeedRight = '*';

    public function _initialize()
    {

        if (isset($_SERVER['HTTP_ORIGIN'])) {
            header('Access-Control-Expose-Headers: __token__');//跨域让客户端获取到
        }
        //跨域检测
        check_cors_request();

        if (!isset($_COOKIE['PHPSESSID'])) {
            Config::set('session.id', $this->request->server("HTTP_SID"));
        }
        parent::_initialize();
    }

    /**
     * 加载初始化
     *
     * @ApiParams (name="version", type="string", required=true, description="版本号")
     * @ApiParams (name="lng", type="string", required=true, description="经度")
     * @ApiParams (name="lat", type="string", required=true, description="纬度")
     */
    public function init()
    {
        if ($version = $this->request->request('version')) {
            $lng = $this->request->request('lng');
            $lat = $this->request->request('lat');

            //配置信息
            $upload = Config::get('upload');
            //如果非服务端中转模式需要修改为中转
            if ($upload['storage'] != 'local' && isset($upload['uploadmode']) && $upload['uploadmode'] != 'server') {
                //临时修改上传模式为服务端中转
                set_addon_config($upload['storage'], ["uploadmode" => "server"], false);

                $upload = \app\common\model\Config::upload();
                // 上传信息配置后
                Hook::listen("upload_config_init", $upload);

                $upload = Config::set('upload', array_merge(Config::get('upload'), $upload));
            }

            $upload['cdnurl'] = $upload['cdnurl'] ? $upload['cdnurl'] : cdnurl('', true);
            $upload['uploadurl'] = preg_match("/^((?:[a-z]+:)?\/\/)(.*)/i", $upload['uploadurl']) ? $upload['uploadurl'] : url($upload['storage'] == 'local' ? '/api/common/upload' : $upload['uploadurl'], '', false, true);

            $content = [
                'citydata'    => Area::getCityFromLngLat($lng, $lat),
                'versiondata' => Version::check($version),
                'uploaddata'  => $upload,
                'coverdata'   => Config::get("cover"),
            ];
            $this->success('', $content);
        } else {
            $this->error(__('Invalid parameters'));
        }
    }

    /**
     * 上传文件
     * @ApiMethod (POST)
     * @ApiParams (name="file", type="file", required=true, description="文件流")
     */
    public function upload()
    {
        Config::set('default_return_type', 'json');
        Config::set('upload.cdnurl', '');

        // 获取 companyName 参数，并做安全过滤
        $companyName = $this->request->post("companyName", "", "trim");
        //$companyName = preg_replace('/[^\w\-]/', '', $companyName); // 只允许字母数字下划线和横杠

        if ($companyName) {
            // 设置保存路径为 uploads/公司名/原文件名.后缀{.suffix}
            $savekey = 'uploads/' . $companyName . '/{filename}';
            \think\Config::set('upload.savekey', $savekey);
        }

        $chunkid = $this->request->post("chunkid");
        if ($chunkid) {
            if (!Config::get('upload.chunking')) {
                $this->error(__('Chunk file disabled'));
            }
            $action = $this->request->post("action");
            $chunkindex = $this->request->post("chunkindex/d");
            $chunkcount = $this->request->post("chunkcount/d");
            $filename = $this->request->post("filename");
            $method = $this->request->method(true);
            if ($action == 'merge') {
                $attachment = null;
                //合并分片文件
                try {
                    $upload = new Upload();
                    $attachment = $upload->merge($chunkid, $chunkcount, $filename);
                } catch (UploadException $e) {
                    $this->error($e->getMessage());
                }
                $this->success(__('Uploaded successful'), ['url' => $attachment->url, 'fullurl' => cdnurl($attachment->url, true)]);
            } elseif ($method == 'clean') {
                //删除冗余的分片文件
                try {
                    $upload = new Upload();
                    $upload->clean($chunkid);
                } catch (UploadException $e) {
                    $this->error($e->getMessage());
                }
                $this->success();
            } else {
                //上传分片文件
                //默认普通上传文件
                $file = $this->request->file('file');
                try {
                    $upload = new Upload($file);
                    $upload->chunk($chunkid, $chunkindex, $chunkcount);
                } catch (UploadException $e) {
                    $this->error($e->getMessage());
                }
                $this->success();
            }
        } else {
            $attachment = null;
            //默认普通上传文件
            $file = $this->request->file('file');
            try {
                $upload = new Upload($file);
                $attachment = $upload->upload();
            } catch (UploadException $e) {
                $this->error($e->getMessage());
            } catch (\Exception $e) {
                $this->error($e->getMessage());
            }

            // 钉钉推送逻辑
            $imgUrl = cdnurl($attachment->url, true);
            // 获取当前登录用户名
            $username = '匿名用户';
            if ($this->auth && $this->auth->isLogin()) {
                $userinfo = $this->auth->getUserinfo();
                $username = $userinfo['nickname'] ?? $userinfo['username'] ?? '匿名用户';
            }
            $this->SendQQ($username, $companyName, $imgUrl);

            $this->success(__('Uploaded successful'), ['url' => $attachment->url, 'fullurl' => $imgUrl]);
        }
    }

    /**
     * NapcatQQ机器人推送
     * @param string $username 上传者用户名
     * @param string $companyName 企业名
     * @param string $imgUrl 图片URL
     */
    private function SendQQ($username, $companyName, $imgUrl)
    {
        // 根据企业名称获取对应的区域和QQ群号
        $qqGroupId = $this->getQQGroupByCompany($companyName);
        
        if (!$qqGroupId) {
            // 如果没有找到对应的QQ群，记录日志并返回
            file_put_contents(RUNTIME_PATH . 'qq_push.log', date('Y-m-d H:i:s') . " 未找到企业 {$companyName} 对应的QQ群配置\n", FILE_APPEND);
            return;
        }
        
        $apiUrl = 'http://127.0.0.1:3000/send_group_msg';
        $data = [
            'group_id' => $qqGroupId, // 动态获取QQ群号
            'message' => [
                ['type' => 'text', 'data' => ['text' => "{$username}---{$companyName}\n"]],
                ['type' => 'image', 'data' => ['file' => $imgUrl]]
            ]
        ];
        $options = [
            'http' => [
                'header'  => "Content-type: application/json",
                'method'  => 'POST',
                'content' => json_encode($data, JSON_UNESCAPED_UNICODE),
                'timeout' => 10
            ],
        ];
        $context  = stream_context_create($options);
        $result = file_get_contents($apiUrl, false, $context);
        
        // 记录推送日志
        $logContent = date('Y-m-d H:i:s') . " 推送消息到QQ群 {$qqGroupId}，企业：{$companyName}，用户：{$username}，结果：{$result}\n";
        file_put_contents(RUNTIME_PATH . 'qq_push.log', $logContent, FILE_APPEND);
    }
    
    /**
     * 根据企业名称获取对应的QQ群号
     * @param string $companyName 企业名称
     * @return string|null QQ群号
     */
    private function getQQGroupByCompany($companyName)
    {
        try {
            // 通过企业名称查询企业信息，获取区域编码
            $company = \think\Db::name('qyxx')
                ->where('EnterpriseName', $companyName)
                ->field('AreaCode')
                ->find();
            
            if (!$company || empty($company['AreaCode'])) {
                return null;
            }
            
            // 通过区域编码查询区域信息，获取QQ群号
            $area = \think\Db::name('area')
                ->where('AreaCode', $company['AreaCode'])
                ->field('qq_group_id')
                ->find();
            
            return $area['qq_group_id'] ?? null;
            
        } catch (\Exception $e) {
            // 记录错误日志
            file_put_contents(RUNTIME_PATH . 'qq_push_error.log', date('Y-m-d H:i:s') . " 获取QQ群号失败：{$e->getMessage()}\n", FILE_APPEND);
            return null;
        }
    }

    /**
     * 验证码
     * @ApiParams (name="id", type="string", required=true, description="要生成验证码的标识")
     * @return \think\Response
     */
    public function captcha($id = "")
    {
        \think\Config::set([
            'captcha' => array_merge(config('captcha'), [
                'fontSize' => 44,
                'imageH'   => 150,
                'imageW'   => 350,
            ])
        ]);
        $captcha = new Captcha((array)Config::get('captcha'));
        return $captcha->entry($id);
    }
}
