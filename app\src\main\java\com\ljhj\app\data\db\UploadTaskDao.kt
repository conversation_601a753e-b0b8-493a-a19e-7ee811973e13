package com.ljhj.app.data.db

import androidx.lifecycle.LiveData
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.ljhj.app.data.model.UploadTask
import java.util.UUID

@Dao
interface UploadTaskDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTask(task: UploadTask): Long

    @Update
    suspend fun updateTask(task: UploadTask)

    @Query("SELECT * FROM upload_tasks ORDER BY timestamp DESC")
    fun getAllTasks(): LiveData<List<UploadTask>>

    @Query("SELECT * FROM upload_tasks WHERE workRequestId = :workRequestId")
    suspend fun getTaskByWorkRequestId(workRequestId: UUID): UploadTask?

    @Query("UPDATE upload_tasks SET status = :status WHERE workRequestId = :workRequestId")
    suspend fun updateStatusByWorkRequestId(workRequestId: UUID, status: String)

    @Query("SELECT * FROM upload_tasks WHERE photoUri = :photoUri")
    suspend fun getTasksByPhotoUri(photoUri: String): List<UploadTask>

    @Query("DELETE FROM upload_tasks WHERE id = :taskId")
    suspend fun deleteTaskById(taskId: Long)

    @Query("SELECT * FROM upload_tasks WHERE uploadDate = :date ORDER BY timestamp DESC")
    suspend fun getTasksByDate(date: String): List<UploadTask>

    @Query("SELECT * FROM upload_tasks WHERE uploadDate = :date AND status = 'SUCCESS' ORDER BY timestamp DESC")
    suspend fun getSuccessfulTasksByDate(date: String): List<UploadTask>

    @Query("UPDATE upload_tasks SET serverUrl = :serverUrl WHERE id = :taskId")
    suspend fun updateServerUrl(taskId: Long, serverUrl: String)
}
