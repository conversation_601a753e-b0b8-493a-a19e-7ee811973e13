<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\exception\UploadException;
use app\common\library\Upload;
use app\common\model\Area;
use app\common\model\Version;
use fast\Random;
use think\captcha\Captcha;
use think\Config;
use think\Hook;

/**
 * 公共接口
 */
class Common extends Api
{
    protected $noNeedLogin = ['init', 'captcha'];
    protected $noNeedRight = '*';

    /**
     * 是否启用调试日志
     * 生产环境应设置为 false
     */
    private $enableDebugLogging = false;

    public function _initialize()
    {


        if (isset($_SERVER['HTTP_ORIGIN'])) {
            header('Access-Control-Expose-Headers: __token__');//跨域让客户端获取到
        }
        //跨域检测
        check_cors_request();

        if (!isset($_COOKIE['PHPSESSID'])) {
            Config::set('session.id', $this->request->server("HTTP_SID"));
        }
        parent::_initialize();
    }

    /**
     * 加载初始化
     *
     * @ApiParams (name="version", type="string", required=true, description="版本号")
     * @ApiParams (name="lng", type="string", required=true, description="经度")
     * @ApiParams (name="lat", type="string", required=true, description="纬度")
     */
    public function init()
    {
        if ($version = $this->request->request('version')) {
            $lng = $this->request->request('lng');
            $lat = $this->request->request('lat');

            //配置信息
            $upload = Config::get('upload');
            //如果非服务端中转模式需要修改为中转
            if ($upload['storage'] != 'local' && isset($upload['uploadmode']) && $upload['uploadmode'] != 'server') {
                //临时修改上传模式为服务端中转
                set_addon_config($upload['storage'], ["uploadmode" => "server"], false);

                $upload = \app\common\model\Config::upload();
                // 上传信息配置后
                Hook::listen("upload_config_init", $upload);

                $upload = Config::set('upload', array_merge(Config::get('upload'), $upload));
            }

            $upload['cdnurl'] = $upload['cdnurl'] ? $upload['cdnurl'] : cdnurl('', true);
            $upload['uploadurl'] = preg_match("/^((?:[a-z]+:)?\/\/)(.*)/i", $upload['uploadurl']) ? $upload['uploadurl'] : url($upload['storage'] == 'local' ? '/api/common/upload' : $upload['uploadurl'], '', false, true);

            $content = [
                'citydata'    => Area::getCityFromLngLat($lng, $lat),
                'versiondata' => Version::check($version),
                'uploaddata'  => $upload,
                'coverdata'   => Config::get("cover"),
            ];
            $this->success('', $content);
        } else {
            $this->error(__('Invalid parameters'));
        }
    }

    /**
     * 上传文件
     * @ApiMethod (POST)
     * @ApiParams (name="file", type="file", required=true, description="文件流")
     */
    public function upload()
    {
        // 先用最基础的方式记录日志，确保能写入
        $basicLog = date('Y-m-d H:i:s') . " - Upload method called\n";
        @file_put_contents(__DIR__ . '/../../runtime/upload_basic.log', $basicLog, FILE_APPEND);

        try {
            // 记录上传开始
            $this->logUploadDebug('上传开始', [
                'method' => $this->request->method(),
                'content_type' => $this->request->header('content-type'),
                'post_data' => $this->request->post(),
                'files' => $_FILES
            ]);
        } catch (\Exception $e) {
            @file_put_contents(__DIR__ . '/../../runtime/upload_basic.log',
                date('Y-m-d H:i:s') . " - logUploadDebug failed: " . $e->getMessage() . "\n",
                FILE_APPEND);
        }

        Config::set('default_return_type', 'json');
        Config::set('upload.cdnurl', '');

        // 获取 companyName 参数，并做安全过滤
        $companyName = $this->request->post("companyName", "", "trim");
        $this->logUploadDebug('获取企业名称', ['companyName' => $companyName]);

        // 检查文件是否存在
        $uploadFile = $this->request->file('file');
        if (!$uploadFile) {
            $this->logUploadDebug('文件获取失败', ['error' => '没有找到上传文件']);
            $this->error('没有找到上传文件');
        }

        try {
            $originalFilename = $uploadFile->getInfo('name');
            $fileSize = $uploadFile->getSize();
            $fileMime = $uploadFile->getMime();

            $this->logUploadDebug('文件信息', [
                'originalFilename' => $originalFilename,
                'size' => $fileSize,
                'type' => $fileMime
            ]);
        } catch (\Exception $e) {
            $this->logUploadDebug('获取文件信息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->error('获取文件信息失败：' . $e->getMessage());
        }

        // 获取区群名
        $groupName = 'default';
        if ($companyName) {
            // 使用统一的方法获取区群名
            $groupName = $this->getGroupNameWithFallback($companyName);
            $this->logUploadDebug('获取区群名', ['groupName' => $groupName]);
        }

        // 目录名安全过滤（只允许字母数字下划线和中文）
        $safeCompanyName = preg_replace('/[^\x{4e00}-\x{9fa5}\w]/u', '', $companyName);
        $safeGroupName = preg_replace('/[^\x{4e00}-\x{9fa5}\w]/u', '', $groupName);
        $yearMonth = date('Ym');

        // 构建保存路径
        $onlyFilename = preg_replace('/[\\\\\\/]/', '', basename($originalFilename)); // 只保留文件名部分

        if ($companyName) {
            // 设置保存路径为 ../LJ-IMG/年月/区群名/企业名/文件名
            $savekey = '../LJ-IMG/' . $yearMonth . '/' . $safeGroupName . '/' . $safeCompanyName . '/' . $onlyFilename;
        } else {
            // 如果没有企业名称，使用简化路径
            $savekey = '../LJ-IMG/' . $yearMonth . '/' . $onlyFilename;
        }

        $this->logUploadDebug('设置保存路径', [
            'savekey' => $savekey,
            'yearMonth' => $yearMonth,
            'safeGroupName' => $safeGroupName,
            'safeCompanyName' => $safeCompanyName,
            'onlyFilename' => $onlyFilename
        ]);

        \think\Config::set('upload.savekey', $savekey);

        $chunkid = $this->request->post("chunkid");
        if ($chunkid) {
            if (!Config::get('upload.chunking')) {
                $this->error(__('Chunk file disabled'));
            }
            $action = $this->request->post("action");
            $chunkindex = $this->request->post("chunkindex/d");
            $chunkcount = $this->request->post("chunkcount/d");
            $filename = $this->request->post("filename");
            $method = $this->request->method(true);
            if ($action == 'merge') {
                $attachment = null;
                //合并分片文件
                try {
                    $upload = new Upload();
                    $attachment = $upload->merge($chunkid, $chunkcount, $filename);
                } catch (UploadException $e) {
                    $this->error($e->getMessage());
                }
                $this->success(__('Uploaded successful'), ['url' => $attachment->url, 'fullurl' => cdnurl($attachment->url, true)]);
            } elseif ($method == 'clean') {
                //删除冗余的分片文件
                try {
                    $upload = new Upload();
                    $upload->clean($chunkid);
                } catch (UploadException $e) {
                    $this->error($e->getMessage());
                }
                $this->success();
            } else {
                //上传分片文件
                //默认普通上传文件
                $file = $this->request->file('file');
                try {
                    $upload = new Upload($file);
                    $upload->chunk($chunkid, $chunkindex, $chunkcount);
                } catch (UploadException $e) {
                    $this->error($e->getMessage());
                }
                $this->success();
            }
        } else {
            $attachment = null;
            $this->logUploadDebug('开始普通上传', ['savekey' => \think\Config::get('upload.savekey')]);

            //默认普通上传文件
            try {
                $upload = new Upload($uploadFile);
                $this->logUploadDebug('Upload对象创建成功', []);

                $attachment = $upload->upload();
                $this->logUploadDebug('上传执行完成', [
                    'attachment_exists' => !is_null($attachment),
                    'attachment_url' => $attachment ? $attachment->url : 'null'
                ]);

            } catch (UploadException $e) {
                $this->logUploadDebug('上传异常 - UploadException', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                $this->error($e->getMessage());
            } catch (\Exception $e) {
                $this->logUploadDebug('上传异常 - Exception', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                $this->error($e->getMessage());
            }

            // 检查上传结果
            if (!$attachment) {
                $this->logUploadDebug('上传失败', ['error' => '未获得文件信息']);
                $this->error('上传失败，未获得文件信息');
            }

            $this->logUploadDebug('上传成功', [
                'url' => $attachment->url,
                'fullurl' => cdnurl($attachment->url, true)
            ]);

            // QQ推送逻辑
            $imgUrl = cdnurl($attachment->url, true);
            // 获取当前登录用户名
            $username = '匿名用户';
            if ($this->auth && $this->auth->isLogin()) {
                $userinfo = $this->auth->getUserinfo();
                $username = $userinfo['nickname'] ?? $userinfo['username'] ?? '匿名用户';
            }

            $this->logUploadDebug('准备QQ推送', [
                'username' => $username,
                'companyName' => $companyName,
                'imgUrl' => $imgUrl
            ]);

            // 获取实际保存的相对路径
            $relativePath = str_replace(['../', './'], '', $attachment->url);
            // 拼接本地绝对路径
            $localPath = "D:/wwwroot/lj.du1.net/" . ltrim($relativePath, '/\\');
            // 如需 file:/// 前缀，取消下行注释
            $localPath = "file:///" . str_replace('\\', '/', $localPath);
            $this->SendQQ($username, $companyName, $localPath);

            $this->success(__('Uploaded successful'), ['url' => $attachment->url, 'fullurl' => $imgUrl]);
        }
    }

    /**
     * NapcatQQ机器人推送
     * @param string $username 上传者用户名
     * @param string $companyName 企业名
     * @param string $imgUrl 图片URL
     */
    private function SendQQ($username, $companyName, $localPath)
    {
        // 调试日志：方法调用和参数
        file_put_contents(RUNTIME_PATH . 'qq_push.log', "SendQQ调用, localPath: $localPath\n", FILE_APPEND);
        // 根据企业名称获取对应的区域和QQ群号
        $qqGroupId = $this->getQQGroupByCompany($companyName);
        
        if (!$qqGroupId) {
            // 如果没有找到对应的QQ群，记录日志并返回
            file_put_contents(RUNTIME_PATH . 'qq_push.log', date('Y-m-d H:i:s') . " 未找到企业 {$companyName} 对应的QQ群配置\n", FILE_APPEND);
            return;
        }
        
        $apiUrl = 'http://127.0.0.1:3000/send_group_msg';
        $data = [
            'group_id' => $qqGroupId, // 动态获取QQ群号
            'message' => [
                ['type' => 'text', 'data' => ['text' => "{$username}---{$companyName}\n"]],
                ['type' => 'image', 'data' => ['file' => $localPath]]
            ]
        ];
        $options = [
            'http' => [
                'header'  => "Content-type: application/json",
                'method'  => 'POST',
                'content' => json_encode($data, JSON_UNESCAPED_UNICODE),
                'timeout' => 10
            ],
        ];
        $context  = stream_context_create($options);
        $result = file_get_contents($apiUrl, false, $context);
        
        // 记录推送日志
        $logContent = date('Y-m-d H:i:s') . " 推送消息到QQ群 {$qqGroupId}，企业：{$companyName}，用户：{$username}，结果：{$result}\n";
        file_put_contents(RUNTIME_PATH . 'qq_push.log', $logContent, FILE_APPEND);

        // WebP转JPG重命名流程（增强版）
        $this->convertWebPToJPG($localPath);
    }

    /**
     * 增强的WebP转JPG重命名方法
     * @param string $localPath 本地文件路径
     */
    private function convertWebPToJPG($localPath)
    {
        $realPath = str_replace('file:///', '', $localPath);
        $pathInfo = pathinfo($realPath);

        // 记录开始转换
        $this->logWebPConversion('开始WebP转换检查', [
            'realPath' => $realPath,
            'pathInfo' => $pathInfo
        ]);

        // 检查是否为WebP文件
        if (!isset($pathInfo['extension']) || strtolower($pathInfo['extension']) !== 'webp') {
            $this->logWebPConversion('非WebP文件，跳过转换', [
                'extension' => $pathInfo['extension'] ?? '无'
            ]);
            return;
        }

        $newPath = $pathInfo['dirname'] . DIRECTORY_SEPARATOR . $pathInfo['filename'] . '.jpg';

        // 使用重试机制进行转换
        $maxRetries = 5;
        $baseDelay = 200000; // 0.2秒，单位微秒
        $success = false;

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            $this->logWebPConversion("转换尝试 $attempt/$maxRetries", [
                'sourcePath' => $realPath,
                'targetPath' => $newPath
            ]);

            // 动态延迟：每次重试增加延迟时间
            $delay = $baseDelay * $attempt;
            usleep($delay);

            // 检查文件状态
            $sourceExists = file_exists($realPath);
            $targetExists = file_exists($newPath);
            $sourceSize = $sourceExists ? filesize($realPath) : 0;

            $this->logWebPConversion("文件状态检查", [
                'sourceExists' => $sourceExists,
                'targetExists' => $targetExists,
                'sourceSize' => $sourceSize,
                'attempt' => $attempt
            ]);

            // 如果源文件不存在，可能已被其他进程处理
            if (!$sourceExists) {
                if ($targetExists) {
                    $this->logWebPConversion('源文件不存在但目标文件存在，转换可能已完成', []);
                    $success = true;
                } else {
                    $this->logWebPConversion('源文件不存在且目标文件也不存在', []);
                }
                break;
            }

            // 如果目标文件已存在，检查是否需要替换
            if ($targetExists) {
                $targetSize = filesize($newPath);
                if ($sourceSize === $targetSize) {
                    // 大小相同，可能转换已完成，删除源文件
                    if (unlink($realPath)) {
                        $this->logWebPConversion('目标文件已存在且大小匹配，删除源文件成功', [
                            'sourceSize' => $sourceSize,
                            'targetSize' => $targetSize
                        ]);
                        $success = true;
                    } else {
                        $this->logWebPConversion('删除源文件失败', []);
                    }
                    break;
                } else {
                    // 大小不同，删除目标文件重新转换
                    if (unlink($newPath)) {
                        $this->logWebPConversion('目标文件大小不匹配，已删除重新转换', [
                            'sourceSize' => $sourceSize,
                            'targetSize' => $targetSize
                        ]);
                    }
                }
            }

            // 检查文件是否被锁定
            if ($this->isFileLocked($realPath)) {
                $this->logWebPConversion("文件被锁定，等待解锁 (尝试 $attempt)", []);
                continue;
            }

            // 检查目录权限
            $parentDir = dirname($newPath);
            if (!is_writable($parentDir)) {
                $this->logWebPConversion('目标目录不可写', ['directory' => $parentDir]);
                break;
            }

            // 尝试重命名
            try {
                if (rename($realPath, $newPath)) {
                    $this->logWebPConversion("重命名成功 (尝试 $attempt)", [
                        'from' => $realPath,
                        'to' => $newPath
                    ]);
                    $success = true;
                    break;
                } else {
                    $error = error_get_last();
                    $this->logWebPConversion("重命名失败 (尝试 $attempt)", [
                        'error' => $error['message'] ?? '未知错误'
                    ]);
                }
            } catch (\Exception $e) {
                $this->logWebPConversion("重命名异常 (尝试 $attempt)", [
                    'exception' => $e->getMessage()
                ]);
            }
        }

        // 记录最终结果
        if ($success) {
            $this->logWebPConversion('WebP转JPG转换完成', ['result' => 'success']);
        } else {
            $this->logWebPConversion('WebP转JPG转换失败', [
                'result' => 'failed',
                'maxRetries' => $maxRetries,
                'finalSourceExists' => file_exists($realPath),
                'finalTargetExists' => file_exists($newPath)
            ]);

            // 记录详细的系统信息用于调试
            $this->logSystemInfoForWebP($realPath, $newPath);
        }
    }

    /**
     * 检查文件是否被锁定
     */
    private function isFileLocked($filePath)
    {
        if (!file_exists($filePath)) {
            return false;
        }

        $handle = @fopen($filePath, 'r+');
        if ($handle === false) {
            return true; // 无法打开，可能被锁定
        }

        $locked = !flock($handle, LOCK_EX | LOCK_NB);
        fclose($handle);

        return $locked;
    }

    /**
     * 记录WebP转换日志
     * 在生产环境中禁用详细调试日志
     */
    private function logWebPConversion($message, $data = [])
    {
        // 生产环境不记录详细调试日志
        if (!$this->enableDebugLogging) {
            return;
        }

        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s.u'),
            'message' => $message,
            'data' => $data,
            'memory' => memory_get_usage(true),
            'pid' => getmypid()
        ];

        file_put_contents(RUNTIME_PATH . 'webp_conversion.log',
            json_encode($logEntry, JSON_UNESCAPED_UNICODE) . "\n",
            FILE_APPEND | LOCK_EX
        );
    }

    /**
     * 记录系统信息用于调试
     */
    private function logSystemInfoForWebP($sourcePath, $targetPath)
    {
        $info = [
            'timestamp' => date('Y-m-d H:i:s'),
            'source_path' => $sourcePath,
            'target_path' => $targetPath,
            'source_exists' => file_exists($sourcePath),
            'target_exists' => file_exists($targetPath),
            'source_size' => file_exists($sourcePath) ? filesize($sourcePath) : 0,
            'source_permissions' => file_exists($sourcePath) ? substr(sprintf('%o', fileperms($sourcePath)), -4) : 'N/A',
            'target_dir_permissions' => is_dir(dirname($targetPath)) ? substr(sprintf('%o', fileperms(dirname($targetPath))), -4) : 'N/A',
            'target_dir_writable' => is_writable(dirname($targetPath)),
            'disk_free_space' => disk_free_space(dirname($targetPath)),
            'php_version' => PHP_VERSION,
            'os' => PHP_OS,
            'server_load' => sys_getloadavg(),
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
        ];

        file_put_contents(RUNTIME_PATH . 'webp_conversion_debug.log',
            "=== WebP转换失败调试信息 ===\n" .
            json_encode($info, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n",
            FILE_APPEND | LOCK_EX
        );
    }

    /**
     * 根据企业名称获取对应的QQ群号
     * @param string $companyName 企业名称
     * @return string|null QQ群号
     */
    private function getQQGroupByCompany($companyName)
    {
        try {
            // 通过企业名称查询企业信息，获取区域编码
            $company = \think\Db::name('qyxx')
                ->where('EnterpriseName', $companyName)
                ->field('AreaCode')
                ->find();
            
            if (!$company || empty($company['AreaCode'])) {
                return null;
            }
            
            // 通过区域编码查询区域信息，获取QQ群号
            $area = \think\Db::name('area')
                ->where('AreaCode', $company['AreaCode'])
                ->field('qq_group_id')
                ->find();
            
            return $area['qq_group_id'] ?? null;
            
        } catch (\Exception $e) {
            // 记录错误日志
            file_put_contents(RUNTIME_PATH . 'qq_push_error.log', date('Y-m-d H:i:s') . " 获取QQ群号失败：{$e->getMessage()}\n", FILE_APPEND);
            return null;
        }
    }

    /**
     * 删除单个图片 - 使用POST方法避免服务器限制
     * @ApiMethod (POST)
     * @ApiParams (name="filename", type="string", required=true, description="文件名")
     * @ApiParams (name="uploadDate", type="string", required=true, description="上传日期")
     * @ApiParams (name="companyName", type="string", required=false, description="企业名称")
     */
    public function delete()
    {

        // 验证用户登录
        if (!$this->auth || !$this->auth->isLogin()) {
            $this->error('请先登录');
        }

        // 获取请求参数
        $input = $this->request->getInput();
        $requestData = json_decode($input, true);

        if (!$requestData) {
            $this->error('请求参数格式错误');
        }

        $filename = $requestData['filename'] ?? '';
        $uploadDate = $requestData['uploadDate'] ?? '';
        $companyName = $requestData['companyName'] ?? '';

        // 验证必要参数
        if (empty($filename)) {
            $this->error('文件名不能为空');
        }

        if (empty($uploadDate)) {
            $this->error('上传日期不能为空');
        }

        // 只允许删除今天的图片
        $today = date('Y-m-d');
        if ($uploadDate !== $today) {
            $this->error('只能删除今天上传的图片');
        }

        try {
            // 记录删除请求详情
            $this->logDeleteRequest($filename, $uploadDate, $companyName);

            // 构建可能的文件路径
            $possiblePaths = $this->buildFilePaths($filename, $uploadDate, $companyName);

            $deletedFiles = [];
            $failedFiles = [];
            $detailedErrors = [];

            // 尝试删除所有可能的文件路径
            foreach ($possiblePaths as $filePath) {
                $deleteResult = $this->deletePhysicalFileWithDetails($filePath);
                if ($deleteResult['success']) {
                    $deletedFiles[] = $filePath;
                } else {
                    $failedFiles[] = $filePath;
                    $detailedErrors[] = [
                        'path' => $filePath,
                        'error' => $deleteResult['error'],
                        'exists' => $deleteResult['exists'],
                        'readable' => $deleteResult['readable'],
                        'writable' => $deleteResult['writable']
                    ];
                }
            }

            // 记录详细的删除结果
            $this->logDeleteResult($filename, $deletedFiles, $failedFiles, $detailedErrors);

            // 删除数据库记录（如果存在）
            $this->deleteFromDatabase($filename, $uploadDate);

            // 返回详细结果
            if (!empty($deletedFiles)) {
                $this->success('删除成功', [
                    'deleted_files' => $deletedFiles,
                    'failed_files' => $failedFiles,
                    'total_deleted' => count($deletedFiles),
                    'searched_paths' => count($possiblePaths)
                ]);
            } else {
                $this->error('未找到要删除的文件或文件已被删除', [
                    'searched_paths' => $possiblePaths,
                    'detailed_errors' => $detailedErrors,
                    'total_searched' => count($possiblePaths)
                ]);
            }

        } catch (\Exception $e) {
            $this->logError('删除文件异常', $e, $filename, $uploadDate);
            $this->error('删除失败：' . $e->getMessage());
        }
    }

    /**
     * 批量删除图片
     * @ApiMethod (DELETE)
     * @ApiParams (name="files", type="array", required=true, description="文件列表")
     */
    public function batchDelete()
    {
        // 验证用户登录
        if (!$this->auth || !$this->auth->isLogin()) {
            $this->error('请先登录');
        }

        // 获取请求参数
        $input = $this->request->getInput();
        $requestData = json_decode($input, true);

        if (!$requestData || !isset($requestData['files']) || !is_array($requestData['files'])) {
            $this->error('请求参数格式错误');
        }

        $files = $requestData['files'];

        if (empty($files)) {
            $this->error('文件列表不能为空');
        }

        // 只允许删除今天的图片
        $today = date('Y-m-d');

        try {
            $successFiles = [];
            $failedFiles = [];
            $totalProcessed = 0;

            foreach ($files as $fileInfo) {
                $filename = $fileInfo['filename'] ?? '';
                $uploadDate = $fileInfo['uploadDate'] ?? '';
                $companyName = $fileInfo['companyName'] ?? '';

                $totalProcessed++;

                // 验证必要参数
                if (empty($filename) || empty($uploadDate)) {
                    $failedFiles[] = [
                        'filename' => $filename,
                        'error' => '文件名或上传日期为空'
                    ];
                    continue;
                }

                // 只允许删除今天的图片
                if ($uploadDate !== $today) {
                    $failedFiles[] = [
                        'filename' => $filename,
                        'error' => '只能删除今天上传的图片'
                    ];
                    continue;
                }

                // 记录删除请求
                $this->logDeleteRequest($filename, $uploadDate, $companyName);

                // 构建可能的文件路径
                $possiblePaths = $this->buildFilePaths($filename, $uploadDate, $companyName);

                $fileDeleted = false;
                $fileErrors = [];

                // 尝试删除文件
                foreach ($possiblePaths as $filePath) {
                    $deleteResult = $this->deletePhysicalFileWithDetails($filePath);
                    if ($deleteResult['success']) {
                        $fileDeleted = true;
                        break;
                    } else {
                        $fileErrors[] = $deleteResult['error'];
                    }
                }

                if ($fileDeleted) {
                    $successFiles[] = $filename;
                    // 删除数据库记录
                    $this->deleteFromDatabase($filename, $uploadDate);
                } else {
                    $failedFiles[] = [
                        'filename' => $filename,
                        'error' => '文件不存在或删除失败: ' . implode(', ', $fileErrors)
                    ];
                }
            }

            // 记录批量删除结果
            $this->logBatchDeleteResult($successFiles, $failedFiles, $totalProcessed);

            // 返回结果
            $successCount = count($successFiles);
            $failedCount = count($failedFiles);

            if ($failedCount === 0) {
                $this->success('批量删除成功', [
                    'success' => $successFiles,
                    'failed' => [],
                    'total' => $totalProcessed
                ]);
            } else if ($successCount === 0) {
                $this->error('批量删除失败', [
                    'success' => [],
                    'failed' => $failedFiles,
                    'total' => $totalProcessed
                ]);
            } else {
                $this->success('批量删除完成', [
                    'success' => $successFiles,
                    'failed' => $failedFiles,
                    'total' => $totalProcessed
                ]);
            }

        } catch (\Exception $e) {
            // 记录错误日志
            $this->logError('批量删除异常', $e, '', '');
            $this->error('批量删除失败：' . $e->getMessage());
        }
    }

    /**
     * 测试删除功能是否可访问
     */
    public function testDelete()
    {
        $testLog = date('Y-m-d H:i:s') . " - testDelete method called\n";
        @file_put_contents(__DIR__ . '/../../runtime/delete_test.log', $testLog, FILE_APPEND);

        $this->success('删除测试成功', [
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => $this->request->method(),
            'input' => $this->request->getInput(),
            'headers' => $this->request->header()
        ]);
    }

    /**
     * 简化的删除方法用于测试
     */
    public function deleteSimple()
    {
        $debugInfo = [
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => $this->request->method(),
            'input' => $this->request->getInput(),
            'post' => $this->request->post(),
            'headers' => $this->request->header()
        ];

        @file_put_contents(__DIR__ . '/../../runtime/delete_simple.log',
            json_encode($debugInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n",
            FILE_APPEND);

        $this->success('简化删除测试成功', $debugInfo);
    }

    /**
     * 测试POST方式的删除（模拟Android请求）
     */
    public function deleteTest()
    {
        $debugInfo = [
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => $this->request->method(),
            'input' => $this->request->getInput(),
            'post' => $this->request->post(),
            'headers' => $this->request->header(),
            'token_header' => $this->request->header('Token'),
            'auth_status' => $this->auth ? $this->auth->isLogin() : 'auth_null'
        ];

        @file_put_contents(__DIR__ . '/../../runtime/delete_test_post.log',
            json_encode($debugInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n",
            FILE_APPEND);

        // 模拟删除逻辑
        $input = $this->request->getInput();
        $requestData = json_decode($input, true);

        if ($requestData) {
            $filename = $requestData['filename'] ?? '';
            $uploadDate = $requestData['uploadDate'] ?? '';
            $companyName = $requestData['companyName'] ?? '';

            $this->success('POST删除测试成功', [
                'received_params' => $requestData,
                'filename' => $filename,
                'uploadDate' => $uploadDate,
                'companyName' => $companyName,
                'debug_info' => $debugInfo
            ]);
        } else {
            $this->error('参数解析失败', $debugInfo);
        }
    }

    /**
     * 创建测试文件用于删除测试
     */
    public function createTestFile()
    {
        try {
            $yearMonth = date('Ym');
            $baseDir = dirname(__DIR__, 2); // 项目根目录
            $testDir = $baseDir . '/LJ-IMG/' . $yearMonth . '/default/测试企业/';
            $testFile = $testDir . 'test.jpg';

            // 记录调试信息
            $debugInfo = [
                'base_dir' => $baseDir,
                'test_dir' => $testDir,
                'test_file' => $testFile,
                'year_month' => $yearMonth,
                'dir_exists_before' => is_dir($testDir),
                'parent_writable' => is_writable(dirname($testDir))
            ];

            @file_put_contents(__DIR__ . '/../../runtime/create_test_file.log',
                json_encode($debugInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n",
                FILE_APPEND);

            // 创建目录
            if (!is_dir($testDir)) {
                $mkdirResult = mkdir($testDir, 0755, true);
                if (!$mkdirResult) {
                    throw new \Exception('无法创建目录: ' . $testDir);
                }
            }

            // 创建测试文件
            $testContent = 'This is a test file for deletion testing. Created at: ' . date('Y-m-d H:i:s');
            $writeResult = file_put_contents($testFile, $testContent);

            if ($writeResult === false) {
                throw new \Exception('无法写入文件: ' . $testFile);
            }

            $this->success('测试文件创建成功', [
                'file_path' => $testFile,
                'file_exists' => file_exists($testFile),
                'file_size' => filesize($testFile),
                'directory' => $testDir,
                'dir_exists' => is_dir($testDir),
                'debug_info' => $debugInfo
            ]);

        } catch (\Exception $e) {
            $errorInfo = [
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ];

            @file_put_contents(__DIR__ . '/../../runtime/create_test_file.log',
                "ERROR: " . json_encode($errorInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n",
                FILE_APPEND);

            $this->error('创建测试文件失败：' . $e->getMessage(), $errorInfo);
        }
    }

    /**
     * 检查LJ-IMG目录结构 - 简化版
     */
    public function checkImageDir()
    {
        $baseDir = dirname(__DIR__, 3); // 项目根目录
        $imgDir = $baseDir . '/LJ-IMG/';

        $dirInfo = [
            'base_dir' => $baseDir,
            'img_dir' => $imgDir,
            'img_dir_exists' => is_dir($imgDir),
            'img_dir_writable' => is_writable($imgDir),
            'base_dir_writable' => is_writable($baseDir),
            'current_dir' => __DIR__,
            'year_month' => date('Ym')
        ];

        // 写入日志
        @file_put_contents(__DIR__ . '/../../runtime/dir_check.log',
            json_encode($dirInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n",
            FILE_APPEND);

        $this->success('目录检查完成', $dirInfo);
    }

    /**
     * 简单的文件创建测试
     */
    public function simpleCreateTest()
    {
        $testFile = __DIR__ . '/../../runtime/simple_test.txt';
        $content = 'Simple test file created at: ' . date('Y-m-d H:i:s');

        $result = file_put_contents($testFile, $content);

        $info = [
            'test_file' => $testFile,
            'write_result' => $result,
            'file_exists' => file_exists($testFile),
            'file_size' => $result ? filesize($testFile) : 0,
            'runtime_dir' => __DIR__ . '/../../runtime/',
            'runtime_writable' => is_writable(__DIR__ . '/../../runtime/')
        ];

        $this->success('简单创建测试完成', $info);
    }

    /**
     * 列出现有的图片文件
     */
    public function listExistingFiles()
    {
        $baseDir = dirname(__DIR__, 3); // 项目根目录
        $imgDir = $baseDir . '/LJ-IMG/';
        $files = [];

        if (is_dir($imgDir)) {
            $this->scanDirectory($imgDir, $files, $imgDir);
        }

        $this->success('文件列表获取完成', [
            'base_dir' => $baseDir,
            'img_dir' => $imgDir,
            'total_files' => count($files),
            'files' => array_slice($files, 0, 20) // 只显示前20个文件
        ]);
    }

    private function scanDirectory($dir, &$files, $baseDir)
    {
        if (!is_dir($dir)) return;

        $items = scandir($dir);
        foreach ($items as $item) {
            if ($item === '.' || $item === '..') continue;

            $fullPath = $dir . '/' . $item;
            if (is_dir($fullPath)) {
                $this->scanDirectory($fullPath, $files, $baseDir);
            } else {
                $relativePath = str_replace($baseDir, '', $fullPath);
                $files[] = [
                    'relative_path' => $relativePath,
                    'full_path' => $fullPath,
                    'size' => filesize($fullPath),
                    'modified' => date('Y-m-d H:i:s', filemtime($fullPath))
                ];
            }
        }
    }

    /**
     * 检查服务器日期和时间
     */
    public function checkServerDate()
    {
        $dateInfo = [
            'server_date' => date('Y-m-d'),
            'server_time' => date('Y-m-d H:i:s'),
            'server_timestamp' => time(),
            'timezone' => date_default_timezone_get(),
            'utc_time' => gmdate('Y-m-d H:i:s'),
            'year_month' => date('Ym')
        ];

        $this->success('服务器日期检查完成', $dateInfo);
    }

    /**
     * 读取最近的删除日志
     */
    public function readDeleteLogs()
    {
        $logFiles = [
            'delete_basic' => __DIR__ . '/../../runtime/delete_basic.log',
            'delete_errors' => __DIR__ . '/../../runtime/delete_errors.log',
            'delete_results' => __DIR__ . '/../../runtime/delete_results.log'
        ];

        $logs = [];
        foreach ($logFiles as $name => $file) {
            if (file_exists($file)) {
                $content = file_get_contents($file);
                // 只获取最后2000个字符
                $logs[$name] = substr($content, -2000);
            } else {
                $logs[$name] = '文件不存在';
            }
        }

        $this->success('日志读取完成', $logs);
    }

    /**
     * 测试上传功能是否可访问
     */
    public function testUpload()
    {
        $testLog = date('Y-m-d H:i:s') . " - testUpload method called\n";
        @file_put_contents(__DIR__ . '/../../runtime/upload_test.log', $testLog, FILE_APPEND);

        $this->success('测试成功', [
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => $this->request->method(),
            'files' => $_FILES,
            'post' => $this->request->post()
        ]);
    }

    /**
     * 简化版上传方法，用于调试
     */
    public function uploadSimple()
    {
        $debugLog = date('Y-m-d H:i:s') . " - uploadSimple method called\n";
        @file_put_contents(__DIR__ . '/../../runtime/upload_simple.log', $debugLog, FILE_APPEND);

        try {
            Config::set('default_return_type', 'json');
            Config::set('upload.cdnurl', '');

            $debugLog .= date('Y-m-d H:i:s') . " - Config set completed\n";
            @file_put_contents(__DIR__ . '/../../runtime/upload_simple.log', $debugLog, FILE_APPEND);

            // 获取文件
            $file = $this->request->file('file');
            if (!$file) {
                $debugLog .= date('Y-m-d H:i:s') . " - No file found\n";
                @file_put_contents(__DIR__ . '/../../runtime/upload_simple.log', $debugLog, FILE_APPEND);
                $this->error('没有找到上传文件');
            }

            $debugLog .= date('Y-m-d H:i:s') . " - File found, starting upload\n";
            @file_put_contents(__DIR__ . '/../../runtime/upload_simple.log', $debugLog, FILE_APPEND);

            // 简单上传
            $upload = new Upload($file);
            $attachment = $upload->upload();

            if (!$attachment) {
                $debugLog .= date('Y-m-d H:i:s') . " - Upload failed, no attachment\n";
                @file_put_contents(__DIR__ . '/../../runtime/upload_simple.log', $debugLog, FILE_APPEND);
                $this->error('上传失败');
            }

            $debugLog .= date('Y-m-d H:i:s') . " - Upload successful: " . $attachment->url . "\n";
            @file_put_contents(__DIR__ . '/../../runtime/upload_simple.log', $debugLog, FILE_APPEND);

            $this->success('上传成功', [
                'url' => $attachment->url,
                'fullurl' => cdnurl($attachment->url, true)
            ]);

        } catch (\Exception $e) {
            $debugLog .= date('Y-m-d H:i:s') . " - Exception: " . $e->getMessage() . "\n";
            @file_put_contents(__DIR__ . '/../../runtime/upload_simple.log', $debugLog, FILE_APPEND);
            $this->error('上传异常：' . $e->getMessage());
        }
    }

    /**
     * 验证码
     * @ApiParams (name="id", type="string", required=true, description="要生成验证码的标识")
     * @return \think\Response
     */
    public function captcha($id = "")
    {
        \think\Config::set([
            'captcha' => array_merge(config('captcha'), [
                'fontSize' => 44,
                'imageH'   => 150,
                'imageW'   => 350,
            ])
        ]);
        $captcha = new Captcha((array)Config::get('captcha'));
        return $captcha->entry($id);
    }

    /**
     * 构建可能的文件路径
     */
    private function buildFilePaths($filename, $uploadDate, $companyName = '')
    {
        $paths = [];
        $yearMonth = date('Ym', strtotime($uploadDate));
        $basePath = '../LJ-IMG/';

        // 处理WebP转JPG的情况，获取原文件名和替代文件名
        $originalFilename = $filename;
        $alternativeFilename = $this->getAlternativeFilename($filename);

        if (!empty($companyName)) {
            // 获取区群名，增加容错处理
            $groupName = $this->getGroupNameWithFallback($companyName);
            $safeCompanyName = preg_replace('/[^\x{4e00}-\x{9fa5}\w]/u', '', $companyName);
            $safeGroupName = preg_replace('/[^\x{4e00}-\x{9fa5}\w]/u', '', $groupName);

            // 构建完整路径（原文件名和替代文件名）
            $paths[] = $basePath . $yearMonth . '/' . $safeGroupName . '/' . $safeCompanyName . '/' . $originalFilename;
            if ($alternativeFilename !== $originalFilename) {
                $paths[] = $basePath . $yearMonth . '/' . $safeGroupName . '/' . $safeCompanyName . '/' . $alternativeFilename;
            }

            // 同时尝试 default 目录（兼容性处理）
            if ($safeGroupName !== 'default') {
                $paths[] = $basePath . $yearMonth . '/default/' . $safeCompanyName . '/' . $originalFilename;
                if ($alternativeFilename !== $originalFilename) {
                    $paths[] = $basePath . $yearMonth . '/default/' . $safeCompanyName . '/' . $alternativeFilename;
                }
            }

            // 尝试不同的企业名称变体（处理特殊字符）
            $companyVariants = $this->getCompanyNameVariants($companyName);
            foreach ($companyVariants as $variant) {
                $safeVariant = preg_replace('/[^\x{4e00}-\x{9fa5}\w]/u', '', $variant);
                if ($safeVariant !== $safeCompanyName) {
                    $paths[] = $basePath . $yearMonth . '/' . $safeGroupName . '/' . $safeVariant . '/' . $originalFilename;
                    if ($alternativeFilename !== $originalFilename) {
                        $paths[] = $basePath . $yearMonth . '/' . $safeGroupName . '/' . $safeVariant . '/' . $alternativeFilename;
                    }
                }
            }
        }

        // 添加简化路径格式
        $paths[] = $basePath . $yearMonth . '/' . $originalFilename;
        $paths[] = $basePath . $originalFilename;

        if ($alternativeFilename !== $originalFilename) {
            $paths[] = $basePath . $yearMonth . '/' . $alternativeFilename;
            $paths[] = $basePath . $alternativeFilename;
        }

        return array_unique($paths);
    }

    /**
     * 获取替代文件名（处理webp/jpg转换）
     */
    private function getAlternativeFilename($filename)
    {
        $pathInfo = pathinfo($filename);
        if (!isset($pathInfo['extension'])) {
            return $filename;
        }

        $extension = strtolower($pathInfo['extension']);
        if ($extension === 'webp') {
            return $pathInfo['filename'] . '.jpg';
        } elseif ($extension === 'jpg' || $extension === 'jpeg') {
            return $pathInfo['filename'] . '.webp';
        }

        return $filename;
    }

    /**
     * 获取区群名称（带容错处理）
     */
    private function getGroupNameWithFallback($companyName)
    {
        try {
            $company = \think\Db::name('qyxx')->where('EnterpriseName', $companyName)->field('AreaCode')->find();
            if ($company && !empty($company['AreaCode'])) {
                $area = \think\Db::name('area')->where('AreaCode', $company['AreaCode'])->field('AreaName')->find();
                if ($area && !empty($area['AreaName'])) {
                    return $area['AreaName'];
                }
            }
        } catch (\Exception $e) {
            $this->logError('获取区群名失败', $e, $companyName, '');
        }

        return 'default';
    }

    /**
     * 获取企业名称的变体（处理特殊字符和编码问题）
     */
    private function getCompanyNameVariants($companyName)
    {
        $variants = [$companyName];

        try {
            // 使用最简单安全的方法，避免特殊字符问题
            $from = array('（', '）', '【', '】', '《', '》');
            $to = array('(', ')', '[', ']', '<', '>');

            // 替换中文括号为英文括号
            $replaced = str_replace($from, $to, $companyName);
            if ($replaced !== $companyName) {
                $variants[] = $replaced;
            }

            // 移除所有括号
            $cleaned = str_replace($from, '', $companyName);
            $cleaned = str_replace($to, '', $cleaned);
            if ($cleaned !== $companyName && $cleaned !== $replaced) {
                $variants[] = $cleaned;
            }

        } catch (\Exception $e) {
            // 如果处理失败，只返回原始名称
            return [$companyName];
        }

        return array_unique($variants);
    }

    /**
     * 删除物理文件（带详细信息）
     */
    private function deletePhysicalFileWithDetails($filePath)
    {
        $result = [
            'success' => false,
            'error' => '',
            'exists' => false,
            'readable' => false,
            'writable' => false,
            'path' => $filePath
        ];

        try {
            // 安全检查：确保文件路径在允许的目录内
            // 先获取绝对路径，不依赖文件是否存在
            $absolutePath = str_replace(['/', '\\'], DIRECTORY_SEPARATOR, $filePath);
            if (!preg_match('/^\.\.[\\/]LJ-IMG[\\/]/', $filePath)) {
                $result['error'] = '安全警告：路径不在允许的目录内';
                $this->logError('安全警告：路径不在允许的目录内', null, $filePath, '');
                return $result;
            }

            // 转换为绝对路径进行检查
            $baseDir = dirname(__DIR__, 3); // 获取项目根目录 (从application/api/controller向上3级到项目根)

            // 更安全的路径处理
            $relativePath = str_replace('../', '', $filePath);
            $relativePath = ltrim($relativePath, '/\\');
            $fullPath = $baseDir . DIRECTORY_SEPARATOR . $relativePath;

            // 标准化路径分隔符
            $fullPath = str_replace(['/', '\\'], DIRECTORY_SEPARATOR, $fullPath);

            // 处理可能的双重分隔符
            $fullPath = preg_replace('/[\\\\\/]+/', DIRECTORY_SEPARATOR, $fullPath);



            // 检查文件状态
            $result['exists'] = file_exists($fullPath);
            $result['readable'] = is_readable($fullPath);
            $result['writable'] = is_writable($fullPath);

            // 检查父目录权限
            $parentDir = dirname($fullPath);

            // 尝试删除文件
            $deleteResult = @unlink($fullPath);

            if ($deleteResult) {
                $result['success'] = true;
                $this->logDeleteSuccess($filePath);
            } else {
                // 如果删除失败，检查文件是否确实不存在了
                $fileExistsAfterDelete = file_exists($fullPath);
                if (!$fileExistsAfterDelete && $result['exists']) {
                    // 文件之前存在，现在不存在了，说明删除成功
                    $result['success'] = true;
                    $this->logDeleteSuccess($filePath);
                } else {
                    $error = error_get_last();
                    $result['error'] = !$result['exists'] ? '文件不存在' : ('删除失败: ' . ($error['message'] ?? '未知错误'));
                }
            }

            return $result;

        } catch (\Exception $e) {
            $result['error'] = '删除异常: ' . $e->getMessage();
            $this->logError('删除物理文件失败', $e, $filePath, '');
            return $result;
        }
    }

    /**
     * 记录删除请求详情
     */
    private function logDeleteRequest($filename, $uploadDate, $companyName)
    {
        $userinfo = $this->auth->getUserinfo();
        $username = $userinfo['nickname'] ?? $userinfo['username'] ?? '未知用户';

        $logContent = date('Y-m-d H:i:s') . " 用户 {$username} 请求删除文件：{$filename}，上传日期：{$uploadDate}，企业：{$companyName}\n";
        file_put_contents(RUNTIME_PATH . 'delete_requests.log', $logContent, FILE_APPEND);
    }

    /**
     * 记录删除结果详情
     */
    private function logDeleteResult($filename, $deletedFiles, $failedFiles, $detailedErrors)
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'filename' => $filename,
            'deleted_files' => $deletedFiles,
            'failed_files' => $failedFiles,
            'detailed_errors' => $detailedErrors,
            'success_count' => count($deletedFiles),
            'failed_count' => count($failedFiles)
        ];

        $logContent = json_encode($logData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        file_put_contents(RUNTIME_PATH . 'delete_results.log', $logContent, FILE_APPEND);
    }

    /**
     * 记录批量删除结果
     */
    private function logBatchDeleteResult($successFiles, $failedFiles, $totalProcessed)
    {
        $userinfo = $this->auth->getUserinfo();
        $username = $userinfo['nickname'] ?? $userinfo['username'] ?? '未知用户';

        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'username' => $username,
            'total_processed' => $totalProcessed,
            'success_files' => $successFiles,
            'failed_files' => $failedFiles,
            'success_count' => count($successFiles),
            'failed_count' => count($failedFiles)
        ];

        $logContent = json_encode($logData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        file_put_contents(RUNTIME_PATH . 'batch_delete_results.log', $logContent, FILE_APPEND);
    }

    /**
     * 记录删除成功日志
     */
    private function logDeleteSuccess($filePath)
    {
        $logContent = date('Y-m-d H:i:s') . " 成功删除文件：{$filePath}\n";
        file_put_contents(RUNTIME_PATH . 'delete_success.log', $logContent, FILE_APPEND);
    }

    /**
     * 记录错误日志
     */
    private function logError($message, $exception, $filename, $uploadDate)
    {
        $userinfo = $this->auth ? $this->auth->getUserinfo() : null;
        $username = $userinfo['nickname'] ?? $userinfo['username'] ?? '未知用户';

        $errorData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'username' => $username,
            'message' => $message,
            'filename' => $filename,
            'uploadDate' => $uploadDate,
            'error' => $exception ? $exception->getMessage() : '',
            'trace' => $exception ? $exception->getTraceAsString() : ''
        ];

        $logContent = json_encode($errorData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        file_put_contents(RUNTIME_PATH . 'delete_errors.log', $logContent, FILE_APPEND);
    }

    /**
     * 删除数据库记录（如果存在相关表）
     */
    private function deleteFromDatabase($filename, $uploadDate)
    {
        try {
            // 这里可以根据实际的数据库表结构来删除相关记录
            // 例如：删除上传记录表中的记录
            // \think\Db::name('upload_records')->where('filename', $filename)->where('upload_date', $uploadDate)->delete();

            // 记录数据库删除操作
            $logContent = date('Y-m-d H:i:s') . " 尝试删除数据库记录：文件名={$filename}，日期={$uploadDate}\n";
            file_put_contents(RUNTIME_PATH . 'database_delete.log', $logContent, FILE_APPEND);

        } catch (\Exception $e) {
            // 数据库删除失败不影响文件删除结果
            $this->logError('删除数据库记录失败', $e, $filename, $uploadDate);
        }
    }

    /**
     * 记录上传调试日志
     * 在生产环境中禁用详细调试日志
     */
    private function logUploadDebug($message, $data = [])
    {
        // 生产环境不记录详细调试日志
        if (!$this->enableDebugLogging) {
            return;
        }

        try {
            $logEntry = [
                'timestamp' => date('Y-m-d H:i:s.u'),
                'message' => $message,
                'data' => $data,
                'memory' => memory_get_usage(true),
                'pid' => getmypid()
            ];

            // 尝试多种路径
            $logPaths = [
                defined('RUNTIME_PATH') ? RUNTIME_PATH . 'upload_debug.log' : null,
                __DIR__ . '/../../runtime/upload_debug.log',
                __DIR__ . '/../runtime/upload_debug.log',
                './runtime/upload_debug.log'
            ];

            $logContent = json_encode($logEntry, JSON_UNESCAPED_UNICODE) . "\n";
            $success = false;

            foreach ($logPaths as $path) {
                if ($path && @file_put_contents($path, $logContent, FILE_APPEND | LOCK_EX)) {
                    $success = true;
                    break;
                }
            }

            // 如果都失败了，写入基础日志
            if (!$success) {
                @file_put_contents(__DIR__ . '/../../runtime/upload_basic.log',
                    date('Y-m-d H:i:s') . " - " . $message . " - " . json_encode($data) . "\n",
                    FILE_APPEND);
            }

        } catch (\Exception $e) {
            // 最后的备用日志
            @file_put_contents(__DIR__ . '/../../runtime/upload_basic.log',
                date('Y-m-d H:i:s') . " - logUploadDebug exception: " . $e->getMessage() . "\n",
                FILE_APPEND);
        }
    }
}
