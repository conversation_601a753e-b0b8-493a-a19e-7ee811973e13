# 绿佳环境应用开发指南

## 开发环境配置

### 必需工具

- **Android Studio**: Arctic Fox 2020.3.1 或更高版本
- **JDK**: Java 11 或更高版本
- **Android SDK**: API Level 24-36
- **Kotlin**: 2.0.21
- **Gradle**: 8.11.1

### 项目配置

1. **克隆项目**
```bash
git clone <repository-url>
cd ljhj-environment-app
```

2. **配置签名**
```bash
# 创建 gradle.properties 文件
echo "KEYSTORE_FILE=release.keystore" >> gradle.properties
echo "KEYSTORE_PASSWORD=your_password" >> gradle.properties
echo "KEY_ALIAS=your_alias" >> gradle.properties
echo "KEY_PASSWORD=your_password" >> gradle.properties
```

3. **同步项目**
```bash
./gradlew sync
```

## 代码规范

### 1. <PERSON><PERSON><PERSON>编码规范

#### 命名规范
```kotlin
// 类名：大驼峰
class ImageProcessor

// 函数名：小驼峰
fun processImage()

// 常量：全大写下划线分隔
const val MAX_IMAGE_SIZE = 1024

// 变量：小驼峰
val imageUri: Uri
```

#### 代码组织
```kotlin
class ExampleClass {
    // 1. 伴生对象
    companion object {
        const val TAG = "ExampleClass"
    }
    
    // 2. 属性
    private val property: String = ""
    
    // 3. 初始化块
    init {
        // 初始化代码
    }
    
    // 4. 公共方法
    fun publicMethod() {}
    
    // 5. 私有方法
    private fun privateMethod() {}
}
```

### 2. Compose编码规范

#### 组件命名
```kotlin
// 组件名：大驼峰，描述性
@Composable
fun EnhancedLoadingIndicator(
    loadingState: LoadingState,
    modifier: Modifier = Modifier
) {
    // 组件实现
}
```

#### 状态管理
```kotlin
@Composable
fun MyScreen() {
    // 状态提升
    var uiState by remember { mutableStateOf(UiState.Idle) }
    
    // 副作用
    LaunchedEffect(key1 = uiState) {
        // 处理副作用
    }
    
    // UI组合
    Column {
        // UI内容
    }
}
```

### 3. 资源管理规范

#### 字符串资源
```xml
<!-- strings.xml -->
<string name="error_network_connection">网络连接失败，请检查网络设置</string>
<string name="permission_camera_rationale">需要相机权限来拍摄照片</string>
```

#### 颜色资源
```xml
<!-- colors.xml -->
<color name="primary_green">#4CAF50</color>
<color name="error_red">#F44336</color>
<color name="warning_orange">#FF9800</color>
```

## 开发流程

### 1. 功能开发流程

```mermaid
graph TD
    A[需求分析] --> B[设计方案]
    B --> C[创建分支]
    C --> D[编写代码]
    D --> E[单元测试]
    E --> F[集成测试]
    F --> G[代码审查]
    G --> H[合并主分支]
```

### 2. Git工作流

#### 分支命名规范
```bash
# 功能分支
feature/camera-enhancement

# 修复分支
bugfix/memory-leak-fix

# 热修复分支
hotfix/critical-crash-fix
```

#### 提交信息规范
```bash
# 格式：type(scope): description
feat(camera): add image compression feature
fix(upload): resolve memory leak in upload worker
docs(api): update API documentation
test(utils): add unit tests for ImageProcessor
```

### 3. 代码审查清单

#### 功能性检查
- [ ] 功能是否按需求实现
- [ ] 边界条件是否处理
- [ ] 错误处理是否完善
- [ ] 性能是否满足要求

#### 代码质量检查
- [ ] 代码是否遵循规范
- [ ] 是否有代码重复
- [ ] 注释是否充分
- [ ] 是否有潜在的内存泄漏

## 调试技巧

### 1. 日志调试

```kotlin
// 使用统一日志系统
Logger.d("Debug information")
Logger.i("Info message")
Logger.w("Warning message")
Logger.e("Error message", exception)

// 分类日志
Logger.network("API request", url)
Logger.camera("Image captured", filename)
Logger.upload("Upload progress", progress)
```

### 2. 内存调试

```kotlin
// 监控内存使用
val memoryInfo = ResourceManager.getMemoryInfo()
Logger.d("Memory usage: ${memoryInfo.getUsagePercentage()}%")

// 检查内存泄漏
if (memoryInfo.isMemoryLow()) {
    Logger.w("Memory is running low")
    ResourceManager.clearBitmapCache()
}
```

### 3. 网络调试

```kotlin
// 添加网络日志拦截器
val loggingInterceptor = HttpLoggingInterceptor { message ->
    Logger.network(message)
}.apply {
    level = if (BuildConfig.DEBUG) {
        HttpLoggingInterceptor.Level.BODY
    } else {
        HttpLoggingInterceptor.Level.NONE
    }
}
```

## 测试指南

### 1. 单元测试

#### 测试工具类
```kotlin
class ImageProcessorTest {
    @Test
    fun `processImage should return compressed bitmap`() {
        // Given
        val originalBitmap = createTestBitmap()
        
        // When
        val result = ImageProcessor.compressBitmap(originalBitmap, 0.8f)
        
        // Then
        assertNotNull(result)
        assertTrue(result.byteCount < originalBitmap.byteCount)
    }
}
```

#### 测试数据库操作
```kotlin
@RunWith(AndroidJUnit4::class)
class UploadTaskDaoTest {
    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()
    
    private lateinit var database: AppDatabase
    private lateinit var dao: UploadTaskDao
    
    @Before
    fun setup() {
        database = Room.inMemoryDatabaseBuilder(
            ApplicationProvider.getApplicationContext(),
            AppDatabase::class.java
        ).allowMainThreadQueries().build()
        
        dao = database.uploadTaskDao()
    }
    
    @Test
    fun insertAndGetTask() = runTest {
        // Given
        val task = UploadTask(
            photoUri = "test_uri",
            workRequestId = UUID.randomUUID(),
            originalFilename = "test.jpg",
            companyName = "Test Company",
            status = "PENDING"
        )
        
        // When
        dao.insertTask(task)
        val tasks = dao.getAllTasks().getOrAwaitValue()
        
        // Then
        assertEquals(1, tasks.size)
        assertEquals(task.originalFilename, tasks[0].originalFilename)
    }
}
```

### 2. UI测试

```kotlin
@RunWith(AndroidJUnit4::class)
class LoginScreenTest {
    @get:Rule
    val composeTestRule = createComposeRule()
    
    @Test
    fun loginScreen_validInput_enablesLoginButton() {
        composeTestRule.setContent {
            LoginScreen(navController = mockNavController)
        }
        
        // 输入用户名和密码
        composeTestRule.onNodeWithText("用户名")
            .performTextInput("testuser")
        composeTestRule.onNodeWithText("密码")
            .performTextInput("password123")
        
        // 验证登录按钮可点击
        composeTestRule.onNodeWithText("登录")
            .assertIsEnabled()
    }
}
```

## 性能优化

### 1. 启动优化

```kotlin
// Application类中初始化
class LjhjApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        
        // 延迟初始化非关键组件
        GlobalScope.launch(Dispatchers.IO) {
            initializeNonCriticalComponents()
        }
    }
}
```

### 2. 内存优化

```kotlin
// 图片加载优化
fun loadImageOptimized(context: Context, uri: Uri): Bitmap? {
    return try {
        val options = BitmapFactory.Options().apply {
            inJustDecodeBounds = true
        }
        
        // 计算合适的采样率
        BitmapFactory.decodeStream(
            context.contentResolver.openInputStream(uri), 
            null, 
            options
        )
        
        options.inSampleSize = calculateInSampleSize(options, 1920, 1080)
        options.inJustDecodeBounds = false
        
        BitmapFactory.decodeStream(
            context.contentResolver.openInputStream(uri), 
            null, 
            options
        )
    } catch (e: Exception) {
        Logger.e("Failed to load image", e)
        null
    }
}
```

### 3. 网络优化

```kotlin
// 配置OkHttp客户端
val okHttpClient = OkHttpClient.Builder()
    .connectTimeout(30, TimeUnit.SECONDS)
    .readTimeout(60, TimeUnit.SECONDS)
    .writeTimeout(60, TimeUnit.SECONDS)
    .cache(Cache(context.cacheDir, 10 * 1024 * 1024)) // 10MB缓存
    .addInterceptor(loggingInterceptor)
    .build()
```

## 发布流程

### 1. 版本管理

```gradle
// build.gradle
android {
    defaultConfig {
        versionCode 3
        versionName "1.7.0"
    }
}
```

### 2. 构建配置

```gradle
// 发布构建
android {
    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }
}
```

### 3. 发布检查清单

- [ ] 版本号已更新
- [ ] 所有测试通过
- [ ] 代码已合并到主分支
- [ ] 签名配置正确
- [ ] ProGuard规则完整
- [ ] 发布说明已准备

## 常见问题解决

### 1. 编译问题

**问题**: Compose编译错误
```bash
# 解决方案：清理并重新构建
./gradlew clean
./gradlew build
```

### 2. 内存问题

**问题**: OutOfMemoryError
```kotlin
// 解决方案：优化图片处理
private fun createBitmapSafely(width: Int, height: Int): Bitmap? {
    return try {
        Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
    } catch (e: OutOfMemoryError) {
        System.gc()
        null
    }
}
```

### 3. 权限问题

**问题**: 权限被拒绝
```kotlin
// 解决方案：检查权限状态
if (ContextCompat.checkSelfPermission(context, permission) 
    != PackageManager.PERMISSION_GRANTED) {
    // 请求权限或引导用户到设置页面
}
```

## 联系方式

如有问题，请联系开发团队：
- 邮箱: <EMAIL>
- 文档: [项目Wiki](wiki-url)
- 问题追踪: [Issue Tracker](issues-url)
