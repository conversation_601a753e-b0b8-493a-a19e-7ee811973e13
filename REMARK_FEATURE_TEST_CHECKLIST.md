# 备注功能测试清单

## 🧪 功能测试清单

### 1. UI界面测试

#### ✅ 备注按钮显示
- [ ] 进入拍照界面，确认顶部有"备注"按钮
- [ ] 初始状态下按钮显示"备注"文字
- [ ] 按钮颜色为默认主色调

#### ✅ 备注输入对话框
- [ ] 点击备注按钮，弹出备注输入对话框
- [ ] 对话框标题显示"添加备注"
- [ ] 输入框有"备注内容"标签
- [ ] 输入框有"请输入备注信息（可选）"占位符
- [ ] 有"确定"和"清空"两个按钮

### 2. 备注输入测试

#### ✅ 正常输入
- [ ] 在输入框中输入文字，确认可以正常输入
- [ ] 测试中文输入
- [ ] 测试英文输入
- [ ] 测试数字输入
- [ ] 测试特殊字符输入

#### ✅ 多行输入
- [ ] 输入多行文本，确认支持换行
- [ ] 验证最多支持3行输入
- [ ] 测试长文本输入的显示效果

#### ✅ 按钮功能
- [ ] 点击"确定"按钮，对话框关闭，备注内容保存
- [ ] 点击"清空"按钮，输入框内容被清空，对话框关闭
- [ ] 点击对话框外部，对话框关闭（取消操作）

### 3. 备注状态测试

#### ✅ 按钮状态变化
- [ ] 输入备注后，按钮文字变为"备注✓"
- [ ] 按钮颜色变为次要色调
- [ ] 清空备注后，按钮恢复为"备注"和原始颜色

#### ✅ 备注持久性
- [ ] 输入备注后，在当前会话中备注内容保持
- [ ] 重新打开备注对话框，之前的内容仍然存在
- [ ] 切换到其他界面再回来，备注内容保持

### 4. 拍照功能测试

#### ✅ 有备注的拍照
- [ ] 输入备注内容
- [ ] 正常拍照
- [ ] 检查生成的照片水印是否包含备注行
- [ ] 验证备注行格式："备注: [输入的内容]"
- [ ] 确认备注行位置在GPS信息之后，时间戳之前

#### ✅ 无备注的拍照
- [ ] 确保没有输入备注（或已清空）
- [ ] 正常拍照
- [ ] 检查生成的照片水印不包含备注行
- [ ] 确认其他水印信息正常显示

#### ✅ 空白备注测试
- [ ] 输入只有空格的备注
- [ ] 拍照后确认水印不显示备注行
- [ ] 输入空字符串备注
- [ ] 拍照后确认水印不显示备注行

### 5. 水印显示测试

#### ✅ 水印内容验证
- [ ] 企业名正确显示
- [ ] 站点名正确显示
- [ ] GPS坐标正确显示
- [ ] 备注内容正确显示（如果有）
- [ ] 时间戳正确显示

#### ✅ 水印顺序验证
- [ ] 企业名在第一行
- [ ] 站点名在第二行
- [ ] GPS坐标在第三行
- [ ] 备注在第四行（如果有）
- [ ] 时间戳在最后一行

#### ✅ 水印格式验证
- [ ] 每行信息格式正确
- [ ] 备注行格式："备注: [内容]"
- [ ] 文字清晰可读
- [ ] 水印位置合适

### 6. 边界情况测试

#### ✅ 特殊输入测试
- [ ] 输入很长的备注文本
- [ ] 输入包含特殊字符的备注
- [ ] 输入包含换行符的备注
- [ ] 输入包含表情符号的备注

#### ✅ 系统状态测试
- [ ] 在低内存情况下测试功能
- [ ] 在网络断开情况下测试功能
- [ ] 在GPS信号弱的情况下测试功能
- [ ] 在存储空间不足时测试功能

### 7. 兼容性测试

#### ✅ 现有功能兼容性
- [ ] 确认水印开关功能正常
- [ ] 确认站点选择功能正常
- [ ] 确认照片类型选择功能正常
- [ ] 确认照片上传功能正常

#### ✅ 向后兼容性
- [ ] 确认之前拍摄的照片仍能正常查看
- [ ] 确认现有的水印格式没有被破坏
- [ ] 确认其他功能模块不受影响

## 🐛 常见问题排查

### 问题1：备注按钮点击无反应
- 检查是否有JavaScript错误
- 确认状态变量是否正确初始化
- 检查事件处理函数是否正确绑定

### 问题2：备注内容不显示在水印中
- 确认备注内容不为空
- 检查ImageProcessor中的参数传递
- 验证buildWatermarkLines函数逻辑

### 问题3：水印格式异常
- 检查水印行的生成顺序
- 确认字符串格式化是否正确
- 验证特殊字符的处理

### 问题4：应用崩溃
- 查看日志中的错误信息
- 检查空指针异常
- 确认所有必需的权限已获取

## 📝 测试报告模板

```
测试日期：[日期]
测试人员：[姓名]
测试设备：[设备型号和系统版本]

功能测试结果：
- UI界面测试：✅/❌
- 备注输入测试：✅/❌
- 拍照功能测试：✅/❌
- 水印显示测试：✅/❌
- 边界情况测试：✅/❌
- 兼容性测试：✅/❌

发现的问题：
1. [问题描述]
2. [问题描述]

建议改进：
1. [改进建议]
2. [改进建议]

总体评价：[通过/需要修复]
```

## 🚀 部署前检查

- [ ] 所有测试用例通过
- [ ] 代码审查完成
- [ ] 性能测试通过
- [ ] 用户接受度测试完成
- [ ] 文档更新完成
- [ ] 版本号更新
- [ ] 发布说明准备完成
