# 绿佳环境应用架构说明

## 整体架构

绿佳环境应用采用现代Android开发架构，基于以下核心技术：

- **UI框架**: Jetpack Compose
- **架构模式**: MVVM (Model-View-ViewModel)
- **依赖注入**: 手动依赖注入（推荐升级到Hilt）
- **数据库**: Room
- **网络请求**: OkHttp
- **异步处理**: Kotlin Coroutines
- **后台任务**: WorkManager

## 项目结构

```
app/src/main/java/com/ljhj/app/
├── MainActivity.kt                 # 主Activity
├── UpdateUtils.kt                 # 更新工具
├── data/                          # 数据层
│   ├── db/                        # 数据库相关
│   │   ├── AppDatabase.kt         # 数据库实例
│   │   ├── Converters.kt          # 类型转换器
│   │   └── UploadTaskDao.kt       # 数据访问对象
│   └── model/                     # 数据模型
│       └── UploadTask.kt          # 上传任务模型
├── ui/                            # UI层
│   ├── LoginScreen.kt             # 登录界面
│   ├── RegisterScreen.kt          # 注册界面
│   ├── components/                # UI组件
│   │   ├── EnhancedMainScreen.kt  # 增强主界面
│   │   ├── LoadingComponents.kt   # 加载组件
│   │   └── ErrorComponents.kt     # 错误处理组件
│   ├── state/                     # 状态管理
│   │   └── UiState.kt             # UI状态定义
│   └── theme/                     # 主题样式
│       ├── Color.kt               # 颜色定义
│       ├── Theme.kt               # 主题配置
│       └── Type.kt                # 字体样式
├── utils/                         # 工具类
│   ├── ErrorHandler.kt            # 错误处理
│   ├── GpsAccuracyManager.kt      # GPS精度管理
│   ├── ImageProcessor.kt          # 图片处理
│   ├── LocationManager.kt         # 位置管理
│   ├── Logger.kt                  # 日志管理
│   ├── PermissionManager.kt       # 权限管理
│   ├── ResourceManager.kt         # 资源管理
│   └── RetryPolicy.kt             # 重试策略
└── workers/                       # 后台任务
    └── UploadWorker.kt            # 上传工作器
```

## 架构层次

### 1. 表现层 (Presentation Layer)

#### MainActivity
- 应用的主入口点
- 管理导航和全局状态
- 处理权限请求和系统回调

#### UI组件 (Compose)
- **LoginScreen**: 用户登录界面
- **RegisterScreen**: 用户注册界面
- **CameraScreen**: 相机拍照界面
- **GalleryScreen**: 相册管理界面
- **LoadingComponents**: 加载状态组件
- **ErrorComponents**: 错误处理组件

#### 状态管理
```kotlin
// 通用UI状态
sealed class UiState<out T> {
    object Idle : UiState<Nothing>()
    object Loading : UiState<Nothing>()
    data class Success<T>(val data: T) : UiState<T>()
    data class Error(val exception: Throwable) : UiState<Nothing>()
}
```

### 2. 业务逻辑层 (Business Logic Layer)

#### 工具类 (Utils)
- **ErrorHandler**: 统一异常处理
- **Logger**: 分类日志管理
- **ResourceManager**: 内存和资源管理
- **PermissionManager**: 权限管理
- **ImageProcessor**: 图片处理和优化
- **LocationManager**: 位置服务管理

#### 管理器 (Managers)
- **GpsAccuracyManager**: GPS精度监控
- **RetryPolicy**: 重试策略配置

### 3. 数据层 (Data Layer)

#### 数据库 (Room)
```kotlin
@Database(entities = [UploadTask::class], version = 1)
abstract class AppDatabase : RoomDatabase() {
    abstract fun uploadTaskDao(): UploadTaskDao
}
```

#### 数据模型
```kotlin
@Entity(tableName = "upload_tasks")
data class UploadTask(
    @PrimaryKey(autoGenerate = true) val id: Long = 0,
    val photoUri: String,
    val workRequestId: UUID,
    val originalFilename: String,
    val companyName: String,
    var status: String,
    val timestamp: Long = System.currentTimeMillis()
)
```

#### 后台任务 (WorkManager)
```kotlin
class UploadWorker : CoroutineWorker {
    override suspend fun doWork(): Result {
        // 执行上传任务
    }
}
```

## 数据流

### 1. 用户操作流程

```
用户操作 → UI组件 → 业务逻辑 → 数据层 → 网络/数据库
    ↓
UI状态更新 ← 结果处理 ← 响应处理 ← 数据返回
```

### 2. 拍照上传流程

```mermaid
graph TD
    A[用户点击拍照] --> B[检查权限]
    B --> C{权限检查}
    C -->|无权限| D[显示权限请求]
    C -->|有权限| E[启动相机]
    E --> F[拍照成功]
    F --> G[图片处理]
    G --> H[添加水印]
    H --> I[保存到相册]
    I --> J[创建上传任务]
    J --> K[WorkManager调度]
    K --> L[后台上传]
    L --> M[更新任务状态]
```

## 关键设计模式

### 1. 单例模式 (Singleton)
```kotlin
object Logger {
    // 全局日志管理
}

object ResourceManager {
    // 全局资源管理
}
```

### 2. 工厂模式 (Factory)
```kotlin
object ErrorHandler {
    fun handleNetworkError(context: Context, throwable: Throwable): String {
        return when (throwable) {
            is UnknownHostException -> "网络连接失败"
            is SocketTimeoutException -> "请求超时"
            else -> "未知错误"
        }
    }
}
```

### 3. 观察者模式 (Observer)
```kotlin
// 使用Flow进行状态观察
val locationUpdates: Flow<LocationResult> = locationManager.getLocationUpdates()
```

## 内存管理策略

### 1. Bitmap管理
- 使用弱引用缓存Bitmap
- 及时回收不用的Bitmap
- 监控内存使用情况

### 2. 协程管理
- 注册和取消协程作用域
- 避免协程泄漏
- 合理使用调度器

### 3. 资源清理
```kotlin
// 在Activity销毁时清理资源
override fun onDestroy() {
    super.onDestroy()
    ResourceManager.clearAll()
}
```

## 错误处理策略

### 1. 分层错误处理
- **UI层**: 显示用户友好的错误信息
- **业务层**: 记录详细错误日志
- **数据层**: 处理数据异常和重试

### 2. 错误分类
- **网络错误**: 连接失败、超时等
- **权限错误**: 权限被拒绝
- **业务错误**: 数据验证失败等
- **系统错误**: OOM、文件IO等

## 性能优化

### 1. 图片处理优化
- 按需缩放图片
- 使用高效的图片格式(WebP)
- 异步处理图片

### 2. 网络优化
- 请求缓存
- 连接池复用
- 重试机制

### 3. 数据库优化
- 使用索引
- 批量操作
- 异步查询

## 安全考虑

### 1. 数据安全
- 敏感数据加密存储
- 网络传输使用HTTPS
- Token安全管理

### 2. 权限安全
- 最小权限原则
- 权限使用说明
- 运行时权限检查

## 测试策略

### 1. 单元测试
- 工具类测试
- 业务逻辑测试
- 数据层测试

### 2. 集成测试
- API接口测试
- 数据库操作测试
- 文件上传测试

### 3. UI测试
- 界面交互测试
- 用户流程测试
- 错误场景测试

## 未来改进方向

### 1. 架构升级
- 引入Hilt依赖注入
- 实现Repository模式
- 添加ViewModel层

### 2. 功能增强
- 离线支持
- 数据同步
- 推送通知

### 3. 性能优化
- 启动速度优化
- 内存使用优化
- 电池使用优化
