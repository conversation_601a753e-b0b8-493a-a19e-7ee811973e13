<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>删除接口测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px; }
        button:hover { background: #005a87; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>删除接口测试</h1>
        
        <div class="form-group">
            <label for="token">Token:</label>
            <input type="text" id="token" value="eceff84b-61ce-4706-99c4-2977485dfc2f" placeholder="输入Token">
        </div>
        
        <div class="form-group">
            <label for="filename">文件名:</label>
            <input type="text" id="filename" value="test.jpg" placeholder="输入文件名">
        </div>
        
        <div class="form-group">
            <label for="uploadDate">上传日期:</label>
            <input type="text" id="uploadDate" value="" placeholder="YYYY-MM-DD">
        </div>
        
        <div class="form-group">
            <label for="companyName">企业名称:</label>
            <input type="text" id="companyName" value="测试企业" placeholder="输入企业名称">
        </div>
        
        <button onclick="testDeletePost()">测试 POST /deleteTest</button>
        <button onclick="testDeleteMethod()">测试 DELETE /delete</button>
        <button onclick="testDeleteSimple()">测试 GET /deleteSimple</button>
        
        <div id="result"></div>
    </div>

    <script>
        // 设置今天的日期
        document.getElementById('uploadDate').value = new Date().toISOString().split('T')[0];
        
        function showResult(success, data) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result ' + (success ? 'success' : 'error');
            resultDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        }
        
        function getFormData() {
            return {
                filename: document.getElementById('filename').value,
                uploadDate: document.getElementById('uploadDate').value,
                companyName: document.getElementById('companyName').value
            };
        }
        
        function getToken() {
            return document.getElementById('token').value;
        }
        
        async function testDeletePost() {
            try {
                const response = await fetch('https://lj.du1.net/api/common/deleteTest', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Token': getToken()
                    },
                    body: JSON.stringify(getFormData())
                });
                
                const data = await response.json();
                showResult(response.ok, { status: response.status, data });
            } catch (error) {
                showResult(false, { error: error.message });
            }
        }
        
        async function testDeleteMethod() {
            try {
                // 使用相对路径避免CORS问题
                const response = await fetch('/api/common/delete', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'Token': getToken()
                    },
                    body: JSON.stringify(getFormData())
                });

                const data = await response.json();
                showResult(response.ok, { status: response.status, data });
            } catch (error) {
                showResult(false, { error: error.message, note: '如果是CORS错误，请使用开发者工具测试' });
            }
        }
        
        async function testDeleteSimple() {
            try {
                const response = await fetch('https://lj.du1.net/api/common/deleteSimple', {
                    method: 'GET',
                    headers: {
                        'Token': getToken()
                    }
                });
                
                const data = await response.json();
                showResult(response.ok, { status: response.status, data });
            } catch (error) {
                showResult(false, { error: error.message });
            }
        }
    </script>
</body>
</html>
