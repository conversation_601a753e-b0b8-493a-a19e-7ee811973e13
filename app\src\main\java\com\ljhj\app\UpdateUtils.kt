package com.ljhj.app

import android.app.DownloadManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import android.os.Build
import android.os.Environment
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.core.content.FileProvider
import okhttp3.Call
import okhttp3.Callback
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import org.json.JSONObject
import androidx.core.content.ContextCompat
import java.io.File
import java.io.IOException

data class VersionInfo(
    val versionCode: Int,
    val versionName: String,
    val updateLog: String,
    val apkUrl: String
)

fun checkVersion(context: Context, onUpdateNeeded: (VersionInfo) -> Unit) {
    val client = OkHttpClient()
    val request = Request.Builder()
        .url("https://lj.du1.net/api/index/getLatestAppVersionJson")
        .get()
        .build()

    client.newCall(request).enqueue(object : Callback {
        override fun onFailure(call: Call, e: IOException) {
            e.printStackTrace()
        }

        override fun onResponse(call: Call, response: Response) {
            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                try {
                    val json = JSONObject(responseBody)
                    val latestVersionCode = json.getInt("versionCode")
                    val currentVersionCode = getCurrentVersionCode(context)

                    if (latestVersionCode > currentVersionCode) {
                        val versionInfo = VersionInfo(
                            versionCode = latestVersionCode,
                            versionName = json.getString("versionName"),
                            updateLog = json.getString("updateLog"),
                            apkUrl = json.getString("apkUrl")
                        )
                        (context as? MainActivity)?.runOnUiThread {
                            onUpdateNeeded(versionInfo)
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    })
}

fun getCurrentVersionCode(context: Context): Long {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
        context.packageManager.getPackageInfo(context.packageName, 0).longVersionCode
    } else {
        @Suppress("DEPRECATION")
        context.packageManager.getPackageInfo(context.packageName, 0).versionCode.toLong()
    }
}

@Composable
fun UpdateDialog(
    versionInfo: VersionInfo,
    onUpdateClick: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("发现新版本 ${versionInfo.versionName}") },
        text = {
            Column {
                Text("更新内容:")
                Spacer(modifier = Modifier.height(8.dp))
                Text(versionInfo.updateLog)
            }
        },
        confirmButton = {
            Button(onClick = onUpdateClick) {
                Text("立即更新")
            }
        },
        dismissButton = {
            Button(onClick = onDismiss) {
                Text("稍后")
            }
        }
    )
}

fun downloadAndInstallApk(context: Context, apkUrl: String, versionName: String) {
    val downloadManager = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
    val request = DownloadManager.Request(Uri.parse(apkUrl))
        .setTitle("正在下载新版本 $versionName")
        .setDescription("下载完成后将自动开始安装")
        .setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
        .setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, "app-release-$versionName.apk")
        .setMimeType("application/vnd.android.package-archive")

    val downloadId = downloadManager.enqueue(request)

    val receiver = object : BroadcastReceiver() {
        override fun onReceive(ctx: Context, intent: Intent) {
            val id = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1)
            if (id == downloadId) {
                val downloadedApkUri = downloadManager.getUriForDownloadedFile(downloadId)
                if (downloadedApkUri != null) {
                    installApk(context, downloadedApkUri)
                }
                context.unregisterReceiver(this)
            }
        }
    }
    ContextCompat.registerReceiver(context, receiver, IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE), ContextCompat.RECEIVER_NOT_EXPORTED)
}

private fun installApk(context: Context, apkUri: Uri) {
    val installIntent = Intent(Intent.ACTION_VIEW).apply {
        setDataAndType(apkUri, "application/vnd.android.package-archive")
        addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    }
    context.startActivity(installIntent)
}
