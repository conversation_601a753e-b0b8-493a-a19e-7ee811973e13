<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\User;
use think\Db;

class Qyxx extends Api
{
    // 需要登录
    protected $noNeedLogin = [];
    protected $noNeedRight = '*';

    /**
     * 添加企业信息
     * @ApiMethod (POST)
     * @ApiParams (name="EnterpriseName", type="string", required=true, description="企业名称")
     * @ApiParams (name="SubName", type="string", required=true, description="站点名称")
     * @ApiParams (name="Location", type="string", required=true, description="坐标位置，经度,纬度")
     */
    public function add()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error('请先登录');
        }

        $EnterpriseName = $this->request->post('EnterpriseName', '', 'trim');
        $SubName = $this->request->post('SubName', '', 'trim');
        $Location = $this->request->post('Location', '', 'trim');

        if (!$EnterpriseName || !$SubName || !$Location) {
            $this->error('参数不完整');
        }

        // 可根据需要自动生成企业编号、站点编号
        $EnterpriseCode = uniqid('E');
        $SubID = uniqid('S');

        $data = [
            'AreaCode'        => '', // 可根据业务需求传递或自动获取
            'EnterpriseName'  => $EnterpriseName,
            'ShortName'       => '', // 可扩展
            'EnterpriseCode'  => $EnterpriseCode,
            'SubID'           => $SubID,
            'SubName'         => $SubName,
            'Location'        => $Location,
            'createtime'      => time(),
            'updatetime'      => time(),
            'deletetime'      => null,
        ];

        // 唯一性校验
        $exists = Db::name('qyxx')->where([
            'EnterpriseCode' => $EnterpriseCode,
            'SubID' => $SubID
        ])->find();
        if ($exists) {
            $this->error('企业编号+站点编号已存在');
        }

        $id = Db::name('qyxx')->insertGetId($data);
        if ($id) {
            $this->success('添加成功', ['id' => $id]);
        } else {
            $this->error('添加失败');
        }
    }

    /**
     * 获取企业信息列表
     * @ApiMethod (GET)
     */
    public function index()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error('请先登录');
        }

        $list = Db::name('qyxx')
            ->field('id,EnterpriseName,SubName,Location')
            ->where(['deletetime' => null])
            ->select();

        $this->success('获取成功', $list);
    }

    /**
     * 获取附近的企业站点
     * @ApiMethod (POST)
     * @ApiParams (name="lat", type="float", required=true, description="用户当前纬度")
     * @ApiParams (name="lon", type="float", required=true, description="用户当前经度")
     * @ApiParams (name="radius", type="integer", required=true, description="搜索半径（米）")
     */
    public function getNearbySites()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error('请先登录');
        }

        $lat = $this->request->post('lat');
        $lon = $this->request->post('lon');
        $radius = $this->request->post('radius', 200, 'intval'); // 默认200米

        if (!$lat || !$lon) {
            $this->error('缺少坐标参数');
        }

        // 使用别名以匹配App端的期望字段 (QYMC, ZDMC)
        $allSites = Db::name('qyxx')
            ->field('id, EnterpriseName as QYMC, SubName as ZDMC, Location')
            ->whereNull('deletetime')
            ->select();

        $nearbySites = [];
        $earthRadius = 6371000; // 地球半径，单位米

        foreach ($allSites as $site) {
            if (empty($site['Location'])) {
                continue;
            }

            $locParts = explode(',', $site['Location']);
            if (count($locParts) !== 2) {
                continue;
            }

            $siteLat = (float)trim($locParts[0]);
            $siteLon = (float)trim($locParts[1]);

            // Haversine 公式计算距离
            $latFrom = deg2rad($lat);
            $lonFrom = deg2rad($lon);
            $latTo = deg2rad($siteLat);
            $lonTo = deg2rad($siteLon);

            $latDelta = $latTo - $latFrom;
            $lonDelta = $lonTo - $lonFrom;

            $angle = 2 * asin(sqrt(pow(sin($latDelta / 2), 2) +
                cos($latFrom) * cos($latTo) * pow(sin($lonDelta / 2), 2)));
            $distance = $angle * $earthRadius;

            if ($distance <= $radius) {
                $site['distance'] = round($distance, 2);
                $nearbySites[] = $site;
            }
        }

        // 按距离排序
        usort($nearbySites, function($a, $b) {
            return $a['distance'] <=> $b['distance'];
        });

        $this->success('获取成功', $nearbySites);
    }
}