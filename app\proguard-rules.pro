# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# ================================
# 生产环境优化配置
# ================================

# 移除调试日志 - 在release版本中移除所有Log.d和Log.v调用
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int d(...);
    public static int i(...);
}

# 移除自定义Logger的调试方法
-assumenosideeffects class com.ljhj.app.utils.Logger {
    public static void d(...);
    public static void i(...);
    public static void camera(...);
    public static void location(...);
    public static void network(...);
    public static void upload(...);
}

# 保留错误和警告日志（生产环境仍需要）
# Log.w 和 Log.e 不会被移除

# ================================
# 保护重要类和方法
# ================================

# 保护数据模型类
-keep class com.ljhj.app.data.model.** { *; }

# 保护数据库实体类
-keep class com.ljhj.app.data.db.** { *; }

# 保护网络相关类
-keep class com.ljhj.app.network.** { *; }

# 保护Compose相关
-keep class androidx.compose.** { *; }
-keep class kotlin.Metadata { *; }

# 保护Room数据库
-keep class * extends androidx.room.RoomDatabase
-keep @androidx.room.Entity class *
-keep @androidx.room.Dao class *

# 保护Retrofit和OkHttp
-keepattributes Signature, InnerClasses, EnclosingMethod
-keepattributes RuntimeVisibleAnnotations, RuntimeVisibleParameterAnnotations
-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}
-dontwarn okhttp3.internal.platform.**
-dontwarn org.conscrypt.**
-dontwarn org.bouncycastle.**
-dontwarn org.openjsse.**

# 保护序列化相关
-keepattributes *Annotation*
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# 保护异常信息（用于错误报告）
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile

# ================================
# 性能优化
# ================================

# 启用优化
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify

# 混淆时保持类名的可读性（便于调试生产问题）
-keepattributes SourceFile,LineNumberTable