package com.ljhj.app.utils

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageManager
import android.location.Location
import android.location.LocationManager as SystemLocationManager
import androidx.core.content.ContextCompat
import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability
import com.google.android.gms.location.*
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.channels.ProducerScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.first
import java.lang.ref.WeakReference
import com.ljhj.app.config.LocationConfig

/**
 * 位置管理器 - 防止内存泄漏的位置服务封装
 */
class LocationManager(context: Context) {
    private val contextRef = WeakReference(context)
    private val fusedLocationClient: FusedLocationProviderClient = LocationServices.getFusedLocationProviderClient(context)
    private val systemLocationManager = context.getSystemService(Context.LOCATION_SERVICE) as SystemLocationManager
    
    private var gmsLocationCallback: LocationCallback? = null
    private var systemLocationListener: android.location.LocationListener? = null
    
    /**
     * 获取位置更新流
     */
    @SuppressLint("MissingPermission")
    fun getLocationUpdates(
        accuracyThreshold: Float = LocationConfig.GpsAccuracy.ACCURACY_THRESHOLD,
        updateInterval: Long = LocationConfig.LocationUpdate.UPDATE_INTERVAL_MS
    ): Flow<LocationResult> = callbackFlow {
        val context = contextRef.get() ?: run {
            Logger.e("Context is null in LocationManager")
            close()
            return@callbackFlow
        }

        if (!hasLocationPermission(context)) {
            Logger.w("Location permission not granted")
            trySend(LocationResult.Error("位置权限未授予"))
            close()
            return@callbackFlow
        }

        val locationRequest = LocationRequest.Builder(Priority.PRIORITY_HIGH_ACCURACY, updateInterval)
            .setMinUpdateIntervalMillis(LocationConfig.LocationUpdate.MIN_UPDATE_INTERVAL_MS)
            .build()

        // 检查用户偏好设置
        val locationProvider = try {
            LocationPreferences.getRecommendedLocationProvider(context)
        } catch (e: Exception) {
            Logger.w("Failed to get location preferences, using system location first as fallback", e)
            LocationPreferences.LocationProvider.SYSTEM_FIRST
        }

        when (locationProvider) {
            LocationPreferences.LocationProvider.SYSTEM_ONLY -> {
                Logger.location("User preference: System location only")
                setupSystemLocationUpdates(this, accuracyThreshold)
            }
            LocationPreferences.LocationProvider.SYSTEM_FIRST -> {
                Logger.location("User preference: System location first, Google as fallback")
                // 优先使用系统定位，但保留Google服务作为备用
                setupSystemLocationUpdatesWithGoogleFallback(this, locationRequest, accuracyThreshold, context)
            }
            LocationPreferences.LocationProvider.GOOGLE_FIRST -> {
                // 尝试使用Google Play Services
                val googleApiAvailability = GoogleApiAvailability.getInstance()
                val resultCode = googleApiAvailability.isGooglePlayServicesAvailable(context)

                if (resultCode == ConnectionResult.SUCCESS) {
                    Logger.location("Google Play Services available, attempting to use for location")
                    setupGoogleLocationUpdates(this, locationRequest, accuracyThreshold, context)
                } else {
                    Logger.location("Google Play Services not available (result code: $resultCode), using system location services")
                    setupSystemLocationUpdates(this, accuracyThreshold)
                }
            }
        }

        awaitClose {
            cleanup()
        }
    }
    
    @SuppressLint("MissingPermission")
    private fun setupGoogleLocationUpdates(
        scope: ProducerScope<LocationResult>,
        locationRequest: LocationRequest,
        accuracyThreshold: Float,
        context: Context
    ) {
        gmsLocationCallback = object : LocationCallback() {
            override fun onLocationResult(locationResult: com.google.android.gms.location.LocationResult) {
                locationResult.lastLocation?.let { location ->
                    Logger.location("Received GMS location: ${location.latitude}, ${location.longitude}, accuracy: ${location.accuracy}")

                    if (location.accuracy <= accuracyThreshold) {
                        scope.trySend(LocationResult.Success(location))
                    } else {
                        scope.trySend(LocationResult.LowAccuracy(location, location.accuracy))
                    }
                }
            }

            override fun onLocationAvailability(availability: LocationAvailability) {
                if (!availability.isLocationAvailable) {
                    Logger.w("Location not available")
                    scope.trySend(LocationResult.Error("位置服务不可用"))
                }
            }
        }

        val builder = LocationSettingsRequest.Builder().addLocationRequest(locationRequest)
        val client: SettingsClient = LocationServices.getSettingsClient(context)
        val task = client.checkLocationSettings(builder.build())

        // 添加超时处理 - 如果Google Play Services检查超时，自动降级到系统定位
        val timeoutHandler = android.os.Handler(context.mainLooper)
        val timeoutRunnable = Runnable {
            Logger.w("Google Play Services location settings check timeout, falling back to system location")
            scope.trySend(LocationResult.Error("Google服务响应超时，切换到系统定位"))
            setupSystemLocationUpdates(scope, accuracyThreshold)
        }

        // 设置Google服务超时
        timeoutHandler.postDelayed(timeoutRunnable, LocationConfig.GpsAccuracy.GOOGLE_SERVICES_TIMEOUT_MS)

        task.addOnSuccessListener {
            Logger.location("Google Play Services location settings satisfied")
            timeoutHandler.removeCallbacks(timeoutRunnable)
            gmsLocationCallback?.let { callback ->
                try {
                    fusedLocationClient.requestLocationUpdates(locationRequest, callback, context.mainLooper)
                    Logger.location("Google Play Services location updates started successfully")
                } catch (e: Exception) {
                    Logger.e("Failed to start Google Play Services location updates", e)
                    setupSystemLocationUpdates(scope, accuracyThreshold)
                }
            }
        }

        task.addOnFailureListener { exception ->
            Logger.e("Location settings check failed", exception)
            timeoutHandler.removeCallbacks(timeoutRunnable)
            // 降级到系统位置服务
            setupSystemLocationUpdates(scope, accuracyThreshold)
        }
    }

    /**
     * 设置系统位置更新（带Google服务备用）
     * 优先使用系统GPS，如果一定时间内无法获取位置，则尝试Google服务
     */
    @SuppressLint("MissingPermission")
    private fun setupSystemLocationUpdatesWithGoogleFallback(
        scope: ProducerScope<LocationResult>,
        locationRequest: LocationRequest,
        accuracyThreshold: Float,
        context: Context
    ) {
        Logger.location("Setting up system location updates with Google fallback")

        var hasReceivedLocation = false
        val timeoutHandler = android.os.Handler(context.mainLooper)

        // 设置系统定位超时，如果10秒内没有收到位置，尝试Google服务
        val fallbackRunnable = Runnable {
            if (!hasReceivedLocation) {
                Logger.location("System location timeout, trying Google services as fallback")
                val googleApiAvailability = GoogleApiAvailability.getInstance()
                val resultCode = googleApiAvailability.isGooglePlayServicesAvailable(context)

                if (resultCode == ConnectionResult.SUCCESS) {
                    Logger.location("Google Play Services available, using as fallback")
                    setupGoogleLocationUpdates(scope, locationRequest, accuracyThreshold, context)
                } else {
                    Logger.location("Google Play Services not available, continuing with system location only")
                }
            }
        }

        systemLocationListener = object : android.location.LocationListener {
            override fun onLocationChanged(location: Location) {
                hasReceivedLocation = true
                timeoutHandler.removeCallbacks(fallbackRunnable)

                Logger.location("System location received: lat=${location.latitude}, lon=${location.longitude}, accuracy=${location.accuracy}")

                if (location.accuracy <= accuracyThreshold) {
                    scope.trySend(LocationResult.Success(location))
                } else {
                    scope.trySend(LocationResult.LowAccuracy(location, location.accuracy))
                }
            }

            override fun onProviderEnabled(provider: String) {
                Logger.location("Location provider enabled: $provider")
            }

            override fun onProviderDisabled(provider: String) {
                Logger.location("Location provider disabled: $provider")
                scope.trySend(LocationResult.Error("位置服务已关闭"))
            }
        }

        systemLocationListener?.let { listener ->
            try {
                systemLocationManager.requestLocationUpdates(
                    SystemLocationManager.GPS_PROVIDER,
                    5000,
                    5f,
                    listener
                )
                systemLocationManager.requestLocationUpdates(
                    SystemLocationManager.NETWORK_PROVIDER,
                    5000,
                    5f,
                    listener
                )

                // 设置10秒超时
                timeoutHandler.postDelayed(fallbackRunnable, 10000L)

            } catch (e: Exception) {
                Logger.e("Failed to request system location updates", e)
                scope.trySend(LocationResult.Error("无法启动位置服务"))
            }
        }
    }

    @SuppressLint("MissingPermission")
    private fun setupSystemLocationUpdates(
        scope: ProducerScope<LocationResult>,
        accuracyThreshold: Float
    ) {
        systemLocationListener = object : android.location.LocationListener {
            override fun onLocationChanged(location: Location) {
                Logger.location("Received system location: ${location.latitude}, ${location.longitude}, accuracy: ${location.accuracy}")

                if (location.accuracy <= accuracyThreshold) {
                    scope.trySend(LocationResult.Success(location))
                } else {
                    scope.trySend(LocationResult.LowAccuracy(location, location.accuracy))
                }
            }

            override fun onProviderEnabled(provider: String) {
                Logger.location("Location provider enabled: $provider")
            }

            override fun onProviderDisabled(provider: String) {
                Logger.location("Location provider disabled: $provider")
                scope.trySend(LocationResult.Error("位置服务已关闭"))
            }
        }

        systemLocationListener?.let { listener ->
            try {
                systemLocationManager.requestLocationUpdates(
                    SystemLocationManager.GPS_PROVIDER,
                    5000,
                    5f,
                    listener
                )
                systemLocationManager.requestLocationUpdates(
                    SystemLocationManager.NETWORK_PROVIDER,
                    5000,
                    5f,
                    listener
                )
            } catch (e: Exception) {
                Logger.e("Failed to request system location updates", e)
                scope.trySend(LocationResult.Error("无法启动位置服务"))
            }
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        Logger.location("Cleaning up LocationManager resources")
        
        gmsLocationCallback?.let { callback ->
            fusedLocationClient.removeLocationUpdates(callback)
            gmsLocationCallback = null
        }
        
        systemLocationListener?.let { listener ->
            systemLocationManager.removeUpdates(listener)
            systemLocationListener = null
        }
    }
    
    private fun hasLocationPermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED ||
                ContextCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 位置结果密封类
     */
    sealed class LocationResult {
        data class Success(val location: Location) : LocationResult()
        data class LowAccuracy(val location: Location, val accuracy: Float) : LocationResult()
        data class Error(val message: String) : LocationResult()
    }
}
