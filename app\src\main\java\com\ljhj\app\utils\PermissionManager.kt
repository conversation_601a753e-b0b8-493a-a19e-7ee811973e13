package com.ljhj.app.utils

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.Settings
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import android.util.Log

/**
 * 权限管理工具
 */
object PermissionManager {

    /**
     * 必需的权限列表
     */
    val REQUIRED_PERMISSIONS = arrayOf(
        Manifest.permission.CAMERA,
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )

    /**
     * 权限说明映射
     */
    private val PERMISSION_DESCRIPTIONS = mapOf(
        Manifest.permission.CAMERA to "相机权限用于拍摄环境监测照片",
        Manifest.permission.ACCESS_FINE_LOCATION to "精确位置权限用于获取拍摄地点的GPS坐标",
        Manifest.permission.ACCESS_COARSE_LOCATION to "大致位置权限用于获取拍摄地点的GPS坐标",
        Manifest.permission.WRITE_EXTERNAL_STORAGE to "存储权限用于保存拍摄的照片"
    )

    /**
     * 检查是否拥有所有必需权限
     */
    fun hasAllRequiredPermissions(context: Context): Boolean {
        return REQUIRED_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 检查特定权限
     */
    fun hasPermission(context: Context, permission: String): Boolean {
        return ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 获取缺失的权限
     */
    fun getMissingPermissions(context: Context): List<String> {
        return REQUIRED_PERMISSIONS.filter { permission ->
            ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 获取权限说明
     */
    fun getPermissionDescription(permission: String): String {
        return PERMISSION_DESCRIPTIONS[permission] ?: "该权限用于应用正常功能"
    }
    
    /**
     * 打开应用设置页面
     */
    fun openAppSettings(context: Context) {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.fromParts("package", context.packageName, null)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
        context.startActivity(intent)
    }
}

/**
 * 增强的权限请求对话框 - 包含详细说明
 */
@Composable
fun EnhancedPermissionRequestDialog(
    permissions: List<String>,
    onPermissionResult: (Map<String, Boolean>) -> Unit,
    onDismiss: () -> Unit,
    permissionLauncher: ManagedActivityResultLauncher<Array<String>, Map<String, Boolean>>
) {
    val context = LocalContext.current

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Security,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("权限申请")
            }
        },
        text = {
            LazyColumn {
                item {
                    Text(
                        text = "为了正常使用应用功能，需要您授予以下权限：",
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )
                }

                items(permissions) { permission ->
                    PermissionExplanationItem(permission = permission)
                }

                item {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "您可以随时在设置中修改这些权限。",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    permissionLauncher.launch(permissions.toTypedArray())
                    onDismiss()
                }
            ) {
                Text("授予权限")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("稍后再说")
            }
        }
    )
}

@Composable
private fun PermissionExplanationItem(permission: String) {
    val (icon, title, description) = getPermissionInfo(permission)

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.Top
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(20.dp)
        )

        Spacer(modifier = Modifier.width(12.dp))

        Column {
            Text(
                text = title,
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}

private fun getPermissionInfo(permission: String): Triple<ImageVector, String, String> {
    return when (permission) {
        Manifest.permission.CAMERA -> Triple(
            Icons.Default.CameraAlt,
            "相机权限",
            "用于拍摄环境监测照片，记录现场情况"
        )
        Manifest.permission.ACCESS_FINE_LOCATION -> Triple(
            Icons.Default.LocationOn,
            "精确位置权限",
            "用于获取拍摄地点的准确GPS坐标，确保数据的地理位置准确性"
        )
        Manifest.permission.ACCESS_COARSE_LOCATION -> Triple(
            Icons.Default.LocationOn,
            "大致位置权限",
            "用于获取拍摄地点的大概位置信息"
        )
        Manifest.permission.WRITE_EXTERNAL_STORAGE -> Triple(
            Icons.Default.Storage,
            "存储权限",
            "用于保存拍摄的照片到设备存储空间"
        )
        else -> Triple(
            Icons.Default.Security,
            "系统权限",
            "应用正常运行所需的系统权限"
        )
    }
}

/**
 * 权限被永久拒绝的对话框
 */
@Composable
fun PermissionDeniedDialog(
    onOpenSettings: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Warning,
                    contentDescription = null,
                    tint = Color(0xFFFF9800),
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("权限被拒绝")
            }
        },
        text = {
            Column {
                Text(
                    text = "应用需要相关权限才能正常工作。请在设置中手动开启权限。",
                    style = MaterialTheme.typography.bodyMedium
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "设置路径：应用信息 → 权限",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
        },
        confirmButton = {
            Button(onClick = onOpenSettings) {
                Text("前往设置")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 权限请求对话框（保持向后兼容）
 */
@Composable
fun PermissionRequestDialog(
    permissions: List<String>,
    onPermissionResult: (Map<String, Boolean>) -> Unit,
    onDismiss: () -> Unit,
    permissionLauncher: ManagedActivityResultLauncher<Array<String>, Map<String, Boolean>>
) {
    EnhancedPermissionRequestDialog(
        permissions = permissions,
        onPermissionResult = onPermissionResult,
        onDismiss = onDismiss,
        permissionLauncher = permissionLauncher
    )
}



/**
 * 权限状态管理
 */
@Composable
fun rememberPermissionState(
    permissions: List<String> = PermissionManager.REQUIRED_PERMISSIONS.toList()
): PermissionState {
    val context = LocalContext.current
    
    return remember(permissions) {
        PermissionState(context, permissions)
    }
}

/**
 * 权限状态类
 */
class PermissionState(
    private val context: Context,
    private val permissions: List<String>
) {
    var hasAllPermissions by mutableStateOf(PermissionManager.hasAllRequiredPermissions(context))
        private set
    
    var deniedPermissions by mutableStateOf<List<String>>(emptyList())
        private set
    
    fun updatePermissionStatus() {
        hasAllPermissions = PermissionManager.hasAllRequiredPermissions(context)
        deniedPermissions = PermissionManager.getMissingPermissions(context)

        Logger.d("Permission status updated - hasAll: $hasAllPermissions, denied: $deniedPermissions", "PermissionManager")
    }

    fun handlePermissionResult(result: Map<String, Boolean>) {
        Logger.d("Permission result received: $result", "PermissionManager")

        val granted = result.filter { it.value }.keys
        val denied = result.filter { !it.value }.keys

        if (granted.isNotEmpty()) {
            Logger.i("Permissions granted: ${granted.joinToString()}", "PermissionManager")
        }

        if (denied.isNotEmpty()) {
            Logger.w("Permissions denied: ${denied.joinToString()}", null, "PermissionManager")
        }

        updatePermissionStatus()
    }
}
