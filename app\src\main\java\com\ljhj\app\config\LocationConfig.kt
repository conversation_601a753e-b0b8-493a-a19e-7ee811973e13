package com.ljhj.app.config

/**
 * 位置相关配置常量
 */
object LocationConfig {
    
    /**
     * 站点搜索相关配置
     */
    object SiteSearch {
        /** 搜索附近站点的半径（米） */
        const val SEARCH_RADIUS = 1000

        /** 显示站点距离的单位阈值（米） */
        const val DISTANCE_DISPLAY_THRESHOLD = 1000
    }
    
    /**
     * GPS精度相关配置
     */
    object GpsAccuracy {
        /** GPS定位精度接受阈值（米） */
        const val ACCURACY_THRESHOLD = 300f

        /** Google Play Services 超时时间（毫秒） - 针对国产手机优化，更快切换到系统GPS */
        const val GOOGLE_SERVICES_TIMEOUT_MS = 5000L  // 5秒，更快切换

        /** GPS整体获取超时时间（毫秒） */
        const val GPS_OVERALL_TIMEOUT_MS = 30000L  // 30秒
    }
    
    /**
     * 位置更新相关配置
     */
    object LocationUpdate {
        /** 位置更新间隔（毫秒） */
        const val UPDATE_INTERVAL_MS = 5000L

        /** 最小更新间隔（毫秒） */
        const val MIN_UPDATE_INTERVAL_MS = 2500L
    }
    
    /**
     * 获取格式化的距离显示文本
     */
    fun formatDistance(distanceInMeters: Double): String {
        return when {
            distanceInMeters < SiteSearch.DISTANCE_DISPLAY_THRESHOLD -> {
                "${distanceInMeters.toInt()}米"
            }
            else -> {
                "%.1f公里".format(distanceInMeters / 1000)
            }
        }
    }
}
