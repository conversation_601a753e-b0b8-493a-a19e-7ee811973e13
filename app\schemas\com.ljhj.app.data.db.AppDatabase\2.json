{"formatVersion": 1, "database": {"version": 2, "identityHash": "c9c49e593799d3f9e798f2ff7b81f477", "entities": [{"tableName": "upload_tasks", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `photoUri` TEXT NOT NULL, `workRequestId` TEXT NOT NULL, `originalFilename` TEXT NOT NULL, `companyName` TEXT NOT NULL, `status` TEXT NOT NULL, `timestamp` INTEGER NOT NULL, `uploadDate` TEXT NOT NULL, `serverUrl` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "photoUri", "columnName": "photoUri", "affinity": "TEXT", "notNull": true}, {"fieldPath": "workRequestId", "columnName": "workRequestId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "originalFilename", "columnName": "originalFilename", "affinity": "TEXT", "notNull": true}, {"fieldPath": "companyName", "columnName": "companyName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "uploadDate", "columnName": "uploadDate", "affinity": "TEXT", "notNull": true}, {"fieldPath": "serverUrl", "columnName": "serverUrl", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'c9c49e593799d3f9e798f2ff7b81f477')"]}}