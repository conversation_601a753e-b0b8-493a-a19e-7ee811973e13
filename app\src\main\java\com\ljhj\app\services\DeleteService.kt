package com.ljhj.app.services

import android.content.Context
import android.net.Uri
import androidx.datastore.preferences.core.stringPreferencesKey
import com.ljhj.app.data.db.AppDatabase
import com.ljhj.app.data.model.UploadTask
import com.ljhj.app.network.NetworkManager
import com.ljhj.app.utils.ErrorHandler
import com.ljhj.app.utils.ErrorMessages
import com.ljhj.app.utils.Logger
import com.ljhj.app.dataStore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

/**
 * 图片删除服务
 */
class DeleteService(private val context: Context) {
    
    private val database = AppDatabase.getDatabase(context)
    private val uploadTaskDao = database.uploadTaskDao()
    private val httpClient by lazy { NetworkManager.createHttpClient(context) }

    companion object {
        private val TOKEN_KEY = stringPreferencesKey("token")
    }
    
    /**
     * 删除结果
     */
    sealed class DeleteResult {
        object Success : DeleteResult()
        data class Error(val message: String, val exception: Throwable? = null) : DeleteResult()
        data class PartialSuccess(val successCount: Int, val failedCount: Int, val errors: List<String>) : DeleteResult()
    }
    
    /**
     * 删除单个图片
     */
    suspend fun deleteImage(task: UploadTask): DeleteResult {
        return try {
            // 检查是否可以删除（只能删除今天的照片）
            if (!task.canDelete()) {
                return DeleteResult.Error("只能删除今天拍摄的照片")
            }

            // 1. 从服务器删除
            val serverDeleteResult = deleteFromServer(task)
            if (serverDeleteResult !is DeleteResult.Success) {
                // 服务器删除失败，但继续删除本地记录
            }
            
            // 2. 从本地存储删除文件
            deleteFromLocalStorage(task.photoUri)

            // 3. 从数据库删除记录
            uploadTaskDao.deleteTaskById(task.id)

            DeleteResult.Success
            
        } catch (e: Exception) {
            val errorMessage = "删除图片失败: ${e.message}"
            Logger.e(errorMessage, e)
            ErrorHandler.handleGenericError(context, e, "删除图片", false)
            DeleteResult.Error(errorMessage, e)
        }
    }
    
    /**
     * 批量删除今天的图片
     */
    suspend fun deleteTodayImages(tasks: List<UploadTask>): DeleteResult {
        val today = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
        val todayTasks = tasks.filter { it.uploadDate == today }

        if (todayTasks.isEmpty()) {
            return DeleteResult.Error("没有今天拍摄的照片可以删除")
        }
        
        var successCount = 0
        var failedCount = 0
        val errors = mutableListOf<String>()
        
        for (task in todayTasks) {
            when (val result = deleteImage(task)) {
                is DeleteResult.Success -> {
                    successCount++
                }
                is DeleteResult.Error -> {
                    failedCount++
                    errors.add("${task.originalFilename}: ${result.message}")
                }
                else -> {
                    failedCount++
                    errors.add("${task.originalFilename}: 未知错误")
                }
            }
        }
        
        return when {
            failedCount == 0 -> DeleteResult.Success
            successCount == 0 -> DeleteResult.Error("所有图片删除失败", null)
            else -> DeleteResult.PartialSuccess(successCount, failedCount, errors)
        }
    }
    
    /**
     * 从服务器删除图片
     */
    private suspend fun deleteFromServer(task: UploadTask): DeleteResult {
        return try {
            // 获取用户token
            val token = context.dataStore.data.map { preferences ->
                preferences[TOKEN_KEY] ?: ""
            }.first()

            if (token.isBlank()) {
                return DeleteResult.Error("用户未登录")
            }
            
            // 构建请求体
            val requestJson = JSONObject().apply {
                put("filename", task.originalFilename)
                put("uploadDate", task.uploadDate)
                put("companyName", task.companyName)
            }
            
            val requestBody = requestJson.toString()
                .toRequestBody("application/json".toMediaTypeOrNull())
            
            // 构建请求 - 改为POST方法避免服务器405错误
            val deleteUrl = NetworkManager.buildUrl(NetworkManager.ApiConfig.DELETE)
            val request = NetworkManager.createRequestBuilder(deleteUrl, token)
                .post(requestBody)
                .build()

            // 执行请求 - 使用IO线程
            val response = withContext(Dispatchers.IO) {
                httpClient.newCall(request).execute()
            }
            
            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                Logger.network("Delete response: $responseBody")
                
                responseBody?.let { body ->
                    val jsonResponse = JSONObject(body)
                    val code = jsonResponse.optInt("code", 0)
                    val message = jsonResponse.optString("msg", "")
                    
                    if (code == 1) {
                        DeleteResult.Success
                    } else {
                        DeleteResult.Error("服务器删除失败: $message")
                    }
                } ?: DeleteResult.Error("服务器响应为空")
                
            } else {
                DeleteResult.Error("服务器删除失败: HTTP ${response.code}")
            }

        } catch (e: IOException) {
            DeleteResult.Error("网络请求失败: ${e.message}", e)
        } catch (e: Exception) {
            DeleteResult.Error("删除请求异常: ${e.message}", e)
        }
    }
    
    /**
     * 从本地存储删除文件
     */
    private fun deleteFromLocalStorage(photoUri: String): Boolean {
        return try {
            val uri = Uri.parse(photoUri)
            context.contentResolver.delete(uri, null, null) > 0
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取今天可删除的图片列表
     */
    suspend fun getTodayDeletableImages(): List<UploadTask> {
        val today = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
        return uploadTaskDao.getTasksByDate(today).filter { it.canDelete() }
    }
    
    /**
     * 检查图片是否可以删除
     */
    fun canDeleteImage(task: UploadTask): Boolean {
        return task.canDelete()
    }
}
