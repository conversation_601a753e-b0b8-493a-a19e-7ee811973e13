package com.ljhj.app

import android.Manifest
import android.annotation.SuppressLint
import android.content.ContentValues
import android.content.Context
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.ImageFormat
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.Typeface
import android.graphics.YuvImage
import android.location.Location
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCaptureException
import androidx.camera.core.ImageProxy
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.livedata.observeAsState
import com.ljhj.app.config.LocationConfig
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import android.location.LocationManager
import android.media.AudioManager
import android.media.SoundPool
import androidx.core.content.ContextCompat
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.core.stringSetPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.work.Constraints
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.workDataOf
import coil.compose.AsyncImage
import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.LocationCallback
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationResult
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import com.google.android.gms.location.Priority
import com.google.android.gms.location.SettingsClient
import com.ljhj.app.data.db.AppDatabase
import com.ljhj.app.data.model.UploadTask
import com.ljhj.app.network.NetworkManager
import com.ljhj.app.ui.LoginScreen
import com.ljhj.app.ui.RegisterScreen
import com.ljhj.app.ui.components.*
import com.ljhj.app.ui.theme.绿佳环境Theme
import com.ljhj.app.utils.*
import com.ljhj.app.workers.UploadWorker
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.Call
import okhttp3.Callback
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import org.json.JSONObject
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.UUID
import java.util.concurrent.Executor

val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "image_uris")

val IMAGE_URIS_KEY = stringSetPreferencesKey("image_uris")
val ADD_WATERMARK_KEY = booleanPreferencesKey("add_watermark")
val TOKEN_KEY = stringPreferencesKey("token")

class MainActivity : ComponentActivity() {

    private lateinit var fusedLocationClient: FusedLocationProviderClient
    internal lateinit var soundPool: SoundPool
    internal var shutterSoundId: Int = 0
    internal var errorSoundId: Int = 0

    private val requestPermissionsLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { permissions ->
            if (permissions[Manifest.permission.CAMERA] == true) {
                // Permissions granted
            } else {
                Toast.makeText(this, "相机权限被拒绝", Toast.LENGTH_SHORT).show()
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        var showUpdateDialog by mutableStateOf<VersionInfo?>(null)

        checkVersion(this) { versionInfo ->
            showUpdateDialog = versionInfo
        }

        fusedLocationClient = LocationServices.getFusedLocationProviderClient(this)

        soundPool = SoundPool.Builder()
            .setMaxStreams(2)
            .build()
        shutterSoundId = soundPool.load(this, R.raw.shutter_sound, 1)
        errorSoundId = soundPool.load(this, R.raw.error_sound, 1)

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            requestPermissionsLauncher.launch(arrayOf(
                Manifest.permission.CAMERA
            ))
        }

        setContent {
            val context = LocalContext.current
            绿佳环境Theme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    val navController = rememberNavController()
                    val tokenFlow = remember { context.dataStore.data.map { preferences ->
                        preferences[TOKEN_KEY] ?: ""
                    } }
                    val token by tokenFlow.collectAsState(initial = "")

                    NavHost(navController = navController, startDestination = "login_screen") {
                        composable("login_screen") {
                            LoginScreen(navController)
                        }
                        composable("register_screen") {
                            RegisterScreen(navController)
                        }
                        composable("main_screen") {
                            MainScreen(navController, token)
                        }
                        composable("camera/{companyName}/{siteName}") { backStackEntry ->
                            val companyName = Uri.decode(backStackEntry.arguments?.getString("companyName") ?: "")
                            val siteName = Uri.decode(backStackEntry.arguments?.getString("siteName") ?: "")
                            CameraScreen(navController, fusedLocationClient, companyName, siteName)
                        }
                        composable("gallery_screen") {
                            GalleryScreen(navController)
                        }
                        composable("full_screen_image/{initialPage}") {
                                backStackEntry ->
                            val initialPage = backStackEntry.arguments?.getString("initialPage")?.toIntOrNull() ?: 0
                            FullScreenImageScreen(navController, initialPage)
                        }
                        composable("add_site_screen/{locationString}") { backStackEntry ->
                            val locationString = Uri.decode(backStackEntry.arguments?.getString("locationString") ?: "")
                            AddSiteScreen(navController, locationString)
                        }
                    }

                    // Navigate based on token
                    LaunchedEffect(token) {
                        if (token.isNotBlank()) {
                            navController.navigate("main_screen") {
                                popUpTo("login_screen") { inclusive = true }
                            }
                        } else {
                             // If token becomes blank (e.g., logout), navigate back to login
                            if (navController.currentDestination?.route != "login_screen") {
                                navController.navigate("login_screen") {
                                    popUpTo(navController.graph.startDestinationId) { inclusive = true }
                                }
                            }
                        }
                    }

                     showUpdateDialog?.let { versionInfo ->
                        UpdateDialog(
                            versionInfo = versionInfo,
                            onUpdateClick = {
                                downloadAndInstallApk(context, versionInfo.apkUrl, versionInfo.versionName)
                                showUpdateDialog = null
                            },
                            onDismiss = {
                                showUpdateDialog = null
                            }
                        )
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        soundPool.release()
    }
}

@Composable
fun MainScreen(navController: NavController, token: String) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val fusedLocationClient = remember { LocationServices.getFusedLocationProviderClient(context) }

    // Status state. This is the only thing the user will see on this screen.
    var statusMessage by remember { mutableStateOf("正在获取GPS...") }

    // State for the selection dialog
    var nearbySites by remember { mutableStateOf<List<JSONObject>>(emptyList()) }
    var showSiteSelectionDialog by remember { mutableStateOf(false) }

    val onLocationAvailable: (Location?, String) -> Boolean = { gpsLocation, currentToken ->
        if (gpsLocation == null) {
            statusMessage = "无法获取GPS位置"
            false
        } else if (gpsLocation.accuracy > LocationConfig.GpsAccuracy.ACCURACY_THRESHOLD) {
            statusMessage = "当前GPS信号弱(精度: ${gpsLocation.accuracy.toInt()}米)，正在等待更精确的位置..."
            false // Rejecting this location, wait for a better one
        } else {
            // Location is valid and accurate enough
            statusMessage = "正在获取附近站点..."
            val locationString = "%.4f,%.4f".format(gpsLocation.latitude, gpsLocation.longitude)

            val url = NetworkManager.buildUrl(NetworkManager.ApiConfig.NEARBY_SITES)
            val requestBody = MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("lat", gpsLocation.latitude.toString())
                .addFormDataPart("lon", gpsLocation.longitude.toString())
                .addFormDataPart("radius", LocationConfig.SiteSearch.SEARCH_RADIUS.toString())
                .build()

            val client = NetworkManager.createHttpClient(context)
            val request = NetworkManager.createRequestBuilder(url, currentToken).post(requestBody).build()

            client.newCall(request).enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    Logger.e("Network request failed", e)
                    coroutineScope.launch { statusMessage = "网络请求失败: ${e.message}" }
                }

                override fun onResponse(call: Call, response: Response) {
                    response.body?.string()?.let {
                        try {
                            val jsonObject = JSONObject(it)
                            if (jsonObject.getInt("code") == 1) {
                                val dataArray = jsonObject.getJSONArray("data")
                                val sites = mutableListOf<JSONObject>()
                                for (i in 0 until dataArray.length()) {
                                    sites.add(dataArray.getJSONObject(i))
                                }

                                sites.forEach { site ->
                                    val locParts = site.optString("Location").split(",")
                                    if (locParts.size == 2) {
                                        val siteLoc = Location("api").apply { latitude = locParts[0].toDouble(); longitude = locParts[1].toDouble() }
                                        site.put("distance", gpsLocation.distanceTo(siteLoc).toDouble())
                                    }
                                }
                                sites.sortBy { site -> site.optDouble("distance", Double.MAX_VALUE) }

                                coroutineScope.launch {
                                    when {
                                        sites.isEmpty() -> {
                                            statusMessage = "附近未找到任何站点"
                                            // 获取当前位置信息，用于传递给 AddSiteScreen
                                            val locationStr = gpsLocation.let { "%.4f,%.4f".format(it.latitude, it.longitude) }
                                            navController.navigate("add_site_screen/${Uri.encode(locationStr)}")
                                        }
                                        sites.size == 1 -> {
                                            val site = sites.first()
                                            navController.navigate("camera/${Uri.encode(site.optString("QYMC"))}/${Uri.encode(site.optString("ZDMC"))}")
                                        }
                                        else -> {
                                            nearbySites = sites
                                            showSiteSelectionDialog = true
                                        }
                                    }
                                }
                            } else {
                                coroutineScope.launch { statusMessage = "错误: ${jsonObject.getString("msg")}" }
                            }
                        } catch (e: Exception) {
                            coroutineScope.launch { statusMessage = "数据解析失败" }
                        }
                    }
                }
            })
            true // Location was accepted and processed
        }
    }



    val currentOnLocationAvailable by rememberUpdatedState(onLocationAvailable)
    val locationRequest = remember { LocationRequest.Builder(Priority.PRIORITY_HIGH_ACCURACY, 60000).build() }
    val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager

    val gmsLocationCallback = remember { object : LocationCallback() {
        override fun onLocationResult(locationResult: LocationResult) {
            val accepted = currentOnLocationAvailable(locationResult.lastLocation, token)
            if (accepted) {
                fusedLocationClient.removeLocationUpdates(this)
            }
        }
    }}

    val locationListener = remember { object : android.location.LocationListener {
        override fun onLocationChanged(loc: Location) {
            val accepted = currentOnLocationAvailable(loc, token)
            if (accepted) {
                locationManager.removeUpdates(this)
            }
        }
    }}

    val centralPermissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        if (permissions[Manifest.permission.ACCESS_FINE_LOCATION] == true || permissions[Manifest.permission.ACCESS_COARSE_LOCATION] == true) {
            // Permission Granted, start location updates
            startLocationUpdates(fusedLocationClient, locationRequest, gmsLocationCallback, locationManager, locationListener, context, token)
        } else {
            statusMessage = "GPS权限被拒绝"
        }
    }

    LaunchedEffect(key1 = token) {
        if (token.isNotEmpty()) {
            statusMessage = "正在验证登录状态..."
            val client = NetworkManager.createHttpClient(context)
            val requestBody = RequestBody.create(null, byteArrayOf())
            val checkRequest = NetworkManager.createRequestBuilder(
                NetworkManager.buildUrl(NetworkManager.ApiConfig.TOKEN_CHECK),
                token
            ).post(requestBody).build()

            client.newCall(checkRequest).enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    coroutineScope.launch {
                        statusMessage = "验证登录失败: ${e.message}"
                    }
                }

                override fun onResponse(call: Call, response: Response) {
                    val responseBody = response.body?.string()
                    try {
                        val jsonObject = JSONObject(responseBody ?: "{}")
                        if (jsonObject.optInt("code") == 1) {
                            // Token is valid, proceed to get location
                            coroutineScope.launch(Dispatchers.Main) {
                                statusMessage = "正在获取GPS..."

                                // Launch timeout job
                                launch {
                                    delay(LocationConfig.GpsAccuracy.GPS_OVERALL_TIMEOUT_MS) // 使用配置的超时时间
                                    if (statusMessage.startsWith("正在获取GPS") || statusMessage.contains("信号弱")) {
                                        fusedLocationClient.removeLocationUpdates(gmsLocationCallback)
                                        locationManager.removeUpdates(locationListener)
                                        statusMessage = "获取GPS位置超时。请检查手机定位服务是否开启，并稍后重试。"
                                    }
                                }

                                val hasPermission = ContextCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED ||
                                        ContextCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED
                                if (!hasPermission) {
                                    centralPermissionLauncher.launch(arrayOf(Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION))
                                } else {
                                    startLocationUpdates(fusedLocationClient, locationRequest, gmsLocationCallback, locationManager, locationListener, context, token)
                                }
                            }
                        } else {
                            // Token is invalid
                            coroutineScope.launch {
                                statusMessage = "登录已失效，请重新登录"
                                context.dataStore.edit { preferences ->
                                    preferences.remove(TOKEN_KEY)
                                }
                            }
                        }
                    } catch (e: Exception) {
                        coroutineScope.launch {
                            statusMessage = "验证响应解析失败"
                        }
                    }
                }
            })
        }
    }

    // --- Simplified UI ---
    Column(
        modifier = Modifier.fillMaxSize().padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(text = statusMessage, style = MaterialTheme.typography.headlineSmall, textAlign = TextAlign.Center)
        if (statusMessage.startsWith("正在")) {
            Spacer(modifier = Modifier.height(16.dp))
            CircularProgressIndicator()
        }
    }

    // --- Dialog with direct navigation ---
    if (showSiteSelectionDialog) {
        AlertDialog(
            onDismissRequest = {},
            title = { Text("请选择您所在的站点") },
            text = {
                LazyColumn {
                    items(nearbySites) { site ->
                        val siteCompanyName = site.optString("QYMC")
                        val siteNameText = site.optString("ZDMC")
                        val distance = site.optDouble("distance")
                        val distanceText = LocationConfig.formatDistance(distance)
                        Text(
                            text = "$siteCompanyName - $siteNameText (约$distanceText)",
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showSiteSelectionDialog = false
                                    navController.navigate("camera/${Uri.encode(site.optString("QYMC"))}/${Uri.encode(site.optString("ZDMC"))}")
                                }
                                .padding(vertical = 12.dp)
                        )
                    }
                }
            },
            confirmButton = {},
            dismissButton = {
                TextButton(onClick = { 
                    showSiteSelectionDialog = false 
                    statusMessage = "已取消选择"
                }) {
                    Text("取消")
                }
            }
        )
    }
}


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CameraScreen(navController: NavController, fusedLocationClient: FusedLocationProviderClient, companyName: String, siteName: String) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val cameraProviderFuture = remember { ProcessCameraProvider.getInstance(context) }
    var imageCapture: ImageCapture? by remember { mutableStateOf(null) }
    var preview: Preview? by remember { mutableStateOf(null) }

    var currentLocation by remember { mutableStateOf<Location?>(null) }
    var lastValidLocation by remember { mutableStateOf<Location?>(null) } // 新增缓存变量

    val photoTypes = listOf("巡检记录", "检修记录", "校准记录", "校验记录",  "现场照片","废液记录", "流水台账", "每日巡检", "标准物质","全系统记录","易耗品")
    var selectedType by remember { mutableStateOf(photoTypes[0]) }
    var isDropdownExpanded by remember { mutableStateOf(false) }

    // 备注相关状态
    var remarkText by remember { mutableStateOf("") }
    var showRemarkDialog by remember { mutableStateOf(false) }

    val scope = rememberCoroutineScope()
    val imageUrisFlow = remember { context.dataStore.data.map { preferences ->
        preferences[IMAGE_URIS_KEY]?.map { Uri.parse(it) } ?: emptyList()
    } }
    val imageUris by imageUrisFlow.collectAsState(initial = emptyList())

    val addWatermarkFlow = remember { context.dataStore.data.map { preferences ->
        preferences[ADD_WATERMARK_KEY] ?: true
    } }
    val addWatermark by addWatermarkFlow.collectAsState(initial = true)

    val locationRequest = remember {
        LocationRequest.Builder(Priority.PRIORITY_HIGH_ACCURACY, 1000)
            .setMinUpdateIntervalMillis(500)
            .build()
    }

    val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager

    val locationListener = remember { object : android.location.LocationListener {
        override fun onLocationChanged(location: Location) {
            if (location.accuracy <= LocationConfig.GpsAccuracy.ACCURACY_THRESHOLD) {
                currentLocation = location
                lastValidLocation = location // 每次有效定位时同步更新缓存
            }
        }
        override fun onStatusChanged(provider: String?, status: Int, extras: Bundle?) {}
        override fun onProviderEnabled(provider: String) {}
        override fun onProviderDisabled(provider: String) {}
    } }

    val gmsLocationCallback = remember {
        object : LocationCallback() {
            override fun onLocationResult(locationResult: LocationResult) {
                locationResult.lastLocation?.let {
                    if (it.accuracy <= 300.0f) {
                        currentLocation = it
                        lastValidLocation = it // 每次有效定位时同步更新缓存
                    }
                }
            }
        }
    }

    LaunchedEffect(Unit) {
        startLocationUpdates(fusedLocationClient, locationRequest, gmsLocationCallback, locationManager, locationListener, context, "")
    }

    DisposableEffect(Unit) {
        onDispose {
            fusedLocationClient.removeLocationUpdates(gmsLocationCallback)
            locationManager.removeUpdates(locationListener)
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        AndroidView(
            factory = { ctx ->
                val previewView = PreviewView(ctx)
                val executor = ContextCompat.getMainExecutor(ctx)
                cameraProviderFuture.addListener({
                    val cameraProvider = cameraProviderFuture.get()
                    preview = Preview.Builder().build().also {
                        it.setSurfaceProvider(previewView.surfaceProvider)
                    }

                    imageCapture = ImageCapture.Builder()
                        .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
                        .build()

                    val cameraSelector = CameraSelector.Builder()
                        .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                        .build()

                    cameraProvider.unbindAll()
                    cameraProvider.bindToLifecycle(
                        lifecycleOwner,
                        cameraSelector,
                        preview,
                        imageCapture
                    )
                }, executor)
                previewView
            },
            modifier = Modifier.fillMaxSize()
        )

        Column(
            modifier = Modifier
                .align(Alignment.BottomStart)
                .padding(start = 16.dp, bottom = 180.dp)
        ) {
            Text(text = "企业名: ${if (companyName.isNotBlank()) companyName else "未设置"}", color = Color.White)
            Text(text = "站点: ${if (siteName.isNotBlank()) siteName else "未设置"}", color = Color.White)
            Text(text = currentLocation?.let { "GPS: %.4f, %.4f".format(it.latitude, it.longitude) } ?: "GPS: ...", color = Color.White)
            Text(text = "时间: ${SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())}", color = Color.White)
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.TopCenter)
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Switch(checked = addWatermark, onCheckedChange = { newValue ->
                    scope.launch {
                        context.dataStore.edit { preferences ->
                            preferences[ADD_WATERMARK_KEY] = newValue
                        }
                    }
                })
                Spacer(modifier = Modifier.width(6.dp))
                Button(
                    onClick = {
                        val locationStr = currentLocation?.let { "%.4f,%.4f".format(it.latitude, it.longitude) } ?: "0,0"
                        navController.navigate("add_site_screen/${Uri.encode(locationStr)}")
                    },
                    modifier = Modifier.height(32.dp),
                    contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp)
                ) {
                    Text("站点", fontSize = 12.sp)
                }
                Spacer(modifier = Modifier.width(6.dp))
                Button(
                    onClick = { showRemarkDialog = true },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (remarkText.isNotBlank()) MaterialTheme.colorScheme.secondary else MaterialTheme.colorScheme.primary
                    ),
                    modifier = Modifier.height(32.dp),
                    contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp)
                ) {
                    Text(if (remarkText.isNotBlank()) "备注✓" else "备注", fontSize = 12.sp)
                }
            }

            Row(modifier = Modifier.weight(1f), horizontalArrangement = Arrangement.End) {
                Box {
                    Button(
                        onClick = { isDropdownExpanded = true },
                        colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.primary),
                        modifier = Modifier.height(32.dp),
                        contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp)
                    ) {
                        Text(selectedType, fontSize = 12.sp, maxLines = 1)
                    }
                    DropdownMenu(expanded = isDropdownExpanded, onDismissRequest = { isDropdownExpanded = false }) {
                        photoTypes.forEach { type ->
                            DropdownMenuItem(text = { Text(type) }, onClick = {
                                selectedType = type
                                isDropdownExpanded = false
                            })
                        }
                    }
                }
                Spacer(modifier = Modifier.width(6.dp))
                Button(
                    onClick = { navController.navigate("gallery_screen") },
                    modifier = Modifier.height(32.dp),
                    contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp)
                ) {
                    Text("相册", fontSize = 12.sp)
                }
            }
        }

        IconButton(
            onClick = {
                val locationForWatermark = currentLocation ?: lastValidLocation // 优先用currentLocation，否则用缓存
                takePhoto(
                    context = context,
                    imageCapture = imageCapture,
                    executor = ContextCompat.getMainExecutor(context),
                    addWatermark = addWatermark,
                    currentLocation = locationForWatermark,
                    companyName = companyName,
                    siteName = siteName,
                    selectedType = selectedType,
                    remarkText = remarkText,
                    soundPool = (context as MainActivity).soundPool,
                    shutterSoundId = (context as MainActivity).shutterSoundId,
                    errorSoundId = (context as MainActivity).errorSoundId,
                    onImageCaptured = { uri ->
                        scope.launch {
                            context.dataStore.edit {
                                val currentUris = it[IMAGE_URIS_KEY] ?: emptySet()
                                it[IMAGE_URIS_KEY] = currentUris + uri.toString()
                            }
                        }
                    }
                )
            },
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 16.dp)
                .size(80.dp)
                .border(2.dp, Color.White, CircleShape)
        ) {
            Icon(Icons.Default.Camera, contentDescription = "拍摄照片", tint = Color.White, modifier = Modifier.size(40.dp))
        }

        LazyRow(
            modifier = Modifier
                .fillMaxWidth()
                .height(100.dp)
                .align(Alignment.BottomStart)
                .padding(start = 16.dp, bottom = 110.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(imageUris) { uri ->
                AsyncImage(
                    model = uri,
                    contentDescription = "已拍摄缩略图",
                    modifier = Modifier
                        .size(80.dp)
                        .clickable { navController.navigate("full_screen_image/${imageUris.indexOf(uri)}") }
                )
            }
        }
    }

    // 备注输入对话框
    if (showRemarkDialog) {
        AlertDialog(
            onDismissRequest = { showRemarkDialog = false },
            title = { Text("添加备注") },
            text = {
                OutlinedTextField(
                    value = remarkText,
                    onValueChange = { remarkText = it },
                    label = { Text("备注内容") },
                    placeholder = { Text("请输入备注信息（可选）") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3
                )
            },
            confirmButton = {
                TextButton(
                    onClick = { showRemarkDialog = false }
                ) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        remarkText = ""
                        showRemarkDialog = false
                    }
                ) {
                    Text("清空")
                }
            }
        )
    }
}

@SuppressLint("MissingPermission")
fun startLocationUpdates(
    fusedLocationClient: FusedLocationProviderClient,
    locationRequest: LocationRequest,
    gmsLocationCallback: LocationCallback,
    locationManager: android.location.LocationManager,
    locationListener: android.location.LocationListener,
    context: Context,
    token: String
) {
    Logger.location("Starting location updates")

    if (!PermissionManager.hasPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) &&
        !PermissionManager.hasPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION)) {
        Logger.w("Location permissions not granted")
        return
    }

    val googleApiAvailability = GoogleApiAvailability.getInstance()
    val resultCode = googleApiAvailability.isGooglePlayServicesAvailable(context)

    if (resultCode == ConnectionResult.SUCCESS) {
        // 检查是否为国产手机，如果是则使用更短的超时时间
        val manufacturer = android.os.Build.MANUFACTURER.lowercase()
        val isChineseManufacturer = manufacturer.contains("huawei") ||
                                  manufacturer.contains("xiaomi") ||
                                  manufacturer.contains("oppo") ||
                                  manufacturer.contains("vivo") ||
                                  manufacturer.contains("oneplus") ||
                                  manufacturer.contains("realme") ||
                                  manufacturer.contains("honor") ||
                                  manufacturer.contains("meizu")

        if (isChineseManufacturer) {
            Logger.location("Chinese manufacturer detected ($manufacturer) in MainActivity, using Google Play Services with shorter timeout")
        }
        Logger.location("Using Google Play Services for location")
        val builder = LocationSettingsRequest.Builder().addLocationRequest(locationRequest)
        val client: SettingsClient = LocationServices.getSettingsClient(context)
        val task = client.checkLocationSettings(builder.build())

        // 添加超时处理 - 如果Google Play Services检查超时，自动降级到系统定位
        val timeoutHandler = android.os.Handler(context.mainLooper)
        val timeoutRunnable = Runnable {
            Logger.w("Google Play Services location settings check timeout in MainActivity, falling back to system location")
            startSystemLocationUpdates(locationManager, locationListener, context)
        }

        // 设置Google服务超时
        timeoutHandler.postDelayed(timeoutRunnable, LocationConfig.GpsAccuracy.GOOGLE_SERVICES_TIMEOUT_MS)

        task.addOnSuccessListener {
            Logger.location("Location settings satisfied, starting GMS location updates")
            timeoutHandler.removeCallbacks(timeoutRunnable)
            try {
                fusedLocationClient.requestLocationUpdates(locationRequest, gmsLocationCallback, context.mainLooper)
                Logger.location("Google Play Services location updates started successfully in MainActivity")
            } catch (e: Exception) {
                Logger.e("Failed to start Google Play Services location updates in MainActivity", e)
                startSystemLocationUpdates(locationManager, locationListener, context)
            }
        }

        task.addOnFailureListener { exception ->
            Logger.w("Location settings not satisfied, falling back to system location", exception)
            timeoutHandler.removeCallbacks(timeoutRunnable)
            // Google服务可用但设置不满足，直接切换为LocationManager
            startSystemLocationUpdates(locationManager, locationListener, context)
        }
    } else {
        Logger.location("Google Play Services not available (result code: $resultCode), using system location")
        // Google服务不可用，直接只用LocationManager
        startSystemLocationUpdates(locationManager, locationListener, context)
    }
}

@SuppressLint("MissingPermission")
private fun startSystemLocationUpdates(
    locationManager: android.location.LocationManager,
    locationListener: android.location.LocationListener,
    context: Context
) {
    try {
        locationManager.requestLocationUpdates(
            android.location.LocationManager.GPS_PROVIDER,
            5000,
            5f,
            locationListener
        )
        locationManager.requestLocationUpdates(
            android.location.LocationManager.NETWORK_PROVIDER,
            5000,
            5f,
            locationListener
        )
        Logger.location("System location updates started")
    } catch (e: Exception) {
        Logger.e("Failed to start system location updates", e)
        ErrorHandler.handleLocationError(
            context,
            e,
            showToast = true
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GalleryScreen(navController: NavController) {
    val context = LocalContext.current
    val database = AppDatabase.getDatabase(context)
    val uploadTaskDao = database.uploadTaskDao()
    val tasks by uploadTaskDao.getAllTasks().observeAsState(initial = emptyList())

    // 删除相关状态
    var selectedTasks by remember { mutableStateOf(setOf<Long>()) }
    var isSelectionMode by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }
    var isDeleting by remember { mutableStateOf(false) }
    var deleteMessage by remember { mutableStateOf("") }

    val deleteService = remember { com.ljhj.app.services.DeleteService(context) }
    val scope = rememberCoroutineScope()

    // 今天可删除的任务
    val todayTasks = remember(tasks) {
        tasks.filter { it.canDelete() }
    }

    Column(modifier = Modifier.fillMaxSize()) {
        TopAppBar(
            title = {
                Text(if (isSelectionMode) "已选择 ${selectedTasks.size} 项" else "相册")
            },
            navigationIcon = {
                IconButton(onClick = {
                    if (isSelectionMode) {
                        isSelectionMode = false
                        selectedTasks = setOf()
                    } else {
                        navController.popBackStack()
                    }
                }) {
                    Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                if (todayTasks.isNotEmpty()) {
                    if (isSelectionMode) {
                        // 全选按钮
                        IconButton(
                            onClick = {
                                selectedTasks = if (selectedTasks.size == todayTasks.size) {
                                    setOf()
                                } else {
                                    todayTasks.map { it.id }.toSet()
                                }
                            }
                        ) {
                            Icon(
                                imageVector = if (selectedTasks.size == todayTasks.size) {
                                    Icons.Default.SelectAll
                                } else {
                                    Icons.Default.CheckBox
                                },
                                contentDescription = "全选"
                            )
                        }

                        // 删除按钮
                        TextButton(
                            onClick = { showDeleteDialog = true },
                            enabled = selectedTasks.isNotEmpty()
                        ) {
                            Text(
                                text = "删除",
                                color = if (selectedTasks.isNotEmpty()) {
                                    Color(0xFFF44336)
                                } else {
                                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                                }
                            )
                        }
                    } else {
                        // 选择模式按钮
                        IconButton(onClick = { isSelectionMode = true }) {
                            Icon(Icons.Default.SelectAll, contentDescription = "选择")
                        }
                    }
                }
            }
        )

        // 删除提示信息
        if (deleteMessage.isNotEmpty()) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = if (deleteMessage.contains("成功")) {
                        Color(0xFF4CAF50).copy(alpha = 0.1f)
                    } else {
                        Color(0xFFF44336).copy(alpha = 0.1f)
                    }
                )
            ) {
                Text(
                    text = deleteMessage,
                    modifier = Modifier.padding(16.dp),
                    color = if (deleteMessage.contains("成功")) {
                        Color(0xFF4CAF50)
                    } else {
                        Color(0xFFF44336)
                    }
                )
            }
        }

        if (tasks.isEmpty()) {
            Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Icon(
                        imageVector = Icons.Default.PhotoLibrary,
                        contentDescription = null,
                        modifier = Modifier.size(64.dp),
                        tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "暂无照片",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }
            }
        } else {
            LazyVerticalGrid(
                columns = GridCells.Fixed(3),
                contentPadding = PaddingValues(4.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp),
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                items(tasks.size) { index ->
                    val task = tasks[index]
                    val isSelected = selectedTasks.contains(task.id)
                    val canDelete = task.canDelete()

                    Box(
                        modifier = Modifier
                            .aspectRatio(1f)
                            .clickable {
                                if (isSelectionMode && canDelete) {
                                    selectedTasks = if (isSelected) {
                                        selectedTasks - task.id
                                    } else {
                                        selectedTasks + task.id
                                    }
                                } else if (!isSelectionMode) {
                                    navController.navigate("full_screen_image/$index")
                                }
                            }
                    ) {
                        AsyncImage(
                            model = Uri.parse(task.photoUri),
                            contentDescription = "照片",
                            modifier = Modifier
                                .fillMaxSize()
                                .then(
                                    if (isSelectionMode && !canDelete) {
                                        Modifier.alpha(0.5f)
                                    } else {
                                        Modifier
                                    }
                                ),
                            contentScale = ContentScale.Crop
                        )

                        // 选择状态覆盖层
                        if (isSelectionMode) {
                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .background(
                                        if (isSelected) {
                                            Color(0xFF2196F3).copy(alpha = 0.3f)
                                        } else if (!canDelete) {
                                            Color(0xFF9E9E9E).copy(alpha = 0.5f)
                                        } else {
                                            Color.Transparent
                                        }
                                    )
                            )

                            // 选择指示器
                            if (canDelete) {
                                Icon(
                                    imageVector = if (isSelected) {
                                        Icons.Default.CheckCircle
                                    } else {
                                        Icons.Default.RadioButtonUnchecked
                                    },
                                    contentDescription = null,
                                    modifier = Modifier
                                        .align(Alignment.TopEnd)
                                        .padding(4.dp)
                                        .size(24.dp),
                                    tint = if (isSelected) Color(0xFF2196F3) else Color.White
                                )
                            } else {
                                // 不可删除标识
                                Icon(
                                    imageVector = Icons.Default.Lock,
                                    contentDescription = "不可删除",
                                    modifier = Modifier
                                        .align(Alignment.Center)
                                        .size(32.dp),
                                    tint = Color.White
                                )
                            }
                        } else {
                            // 状态指示器
                            when (task.status) {
                                "PENDING", "UPLOADING" -> {
                                    Box(
                                        modifier = Modifier
                                            .fillMaxSize()
                                            .background(Color(0x80000000)),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                            CircularProgressIndicator(
                                                modifier = Modifier.size(24.dp),
                                                color = Color.White,
                                                strokeWidth = 2.dp
                                            )
                                            Spacer(modifier = Modifier.height(4.dp))
                                            Text("上传中...", color = Color.White, fontSize = 12.sp)
                                        }
                                    }
                                }
                                "FAILED" -> {
                                    Box(
                                        modifier = Modifier
                                            .fillMaxSize()
                                            .background(Color(0x80F44336)),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                            Icon(
                                                imageVector = Icons.Default.Error,
                                                contentDescription = null,
                                                tint = Color.White,
                                                modifier = Modifier.size(24.dp)
                                            )
                                            Spacer(modifier = Modifier.height(4.dp))
                                            Text("上传失败", color = Color.White, fontSize = 10.sp)
                                            Button(
                                                onClick = { retryUpload(context, task) },
                                                modifier = Modifier.padding(top = 4.dp),
                                                colors = ButtonDefaults.buttonColors(
                                                    containerColor = Color.White.copy(alpha = 0.2f)
                                                )
                                            ) {
                                                Text("重试", fontSize = 10.sp, color = Color.White)
                                            }
                                        }
                                    }
                                }
                                "SUCCESS" -> {
                                    Box(
                                        modifier = Modifier
                                            .align(Alignment.TopEnd)
                                            .padding(4.dp)
                                            .background(
                                                Color(0xFF4CAF50).copy(alpha = 0.8f),
                                                CircleShape
                                            )
                                            .size(16.dp),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Check,
                                            contentDescription = "上传成功",
                                            tint = Color.White,
                                            modifier = Modifier.size(12.dp)
                                        )
                                    }
                                }
                            }

                            // 今天拍摄的照片标识
                            if (canDelete) {
                                Text(
                                    text = "今天",
                                    modifier = Modifier
                                        .align(Alignment.BottomStart)
                                        .padding(4.dp)
                                        .background(
                                            Color.Black.copy(alpha = 0.6f),
                                            RoundedCornerShape(4.dp)
                                        )
                                        .padding(horizontal = 6.dp, vertical = 2.dp),
                                    color = Color.White,
                                    fontSize = 10.sp
                                )
                            }
                        }
                    }
                }
            }
        }

        // 删除确认对话框
        if (showDeleteDialog) {
            AlertDialog(
                onDismissRequest = { showDeleteDialog = false },
                title = {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Icon(
                            imageVector = Icons.Default.Warning,
                            contentDescription = null,
                            tint = Color(0xFFFF9800),
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("确认删除")
                    }
                },
                text = {
                    Column {
                        Text("确定要删除选中的 ${selectedTasks.size} 张照片吗？")
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "删除后将同时从设备和服务器中移除，且无法恢复。",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                    }
                },
                confirmButton = {
                    Button(
                        onClick = {
                            showDeleteDialog = false
                            scope.launch {
                                isDeleting = true
                                deleteMessage = ""

                                try {
                                    val tasksToDelete = tasks.filter { selectedTasks.contains(it.id) }
                                    val result = deleteService.deleteTodayImages(tasksToDelete)

                                    deleteMessage = when (result) {
                                        is com.ljhj.app.services.DeleteService.DeleteResult.Success -> {
                                            "成功删除 ${selectedTasks.size} 张照片"
                                        }
                                        is com.ljhj.app.services.DeleteService.DeleteResult.PartialSuccess -> {
                                            "删除完成：成功 ${result.successCount} 张，失败 ${result.failedCount} 张"
                                        }
                                        is com.ljhj.app.services.DeleteService.DeleteResult.Error -> {
                                            "删除失败：${result.message}"
                                        }
                                    }

                                    // 清除选择状态
                                    selectedTasks = setOf()
                                    isSelectionMode = false

                                } catch (e: Exception) {
                                    deleteMessage = "删除过程中发生错误：${e.message}"
                                    Logger.e("Delete error", e)
                                } finally {
                                    isDeleting = false
                                }
                            }
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFF44336)
                        ),
                        enabled = !isDeleting
                    ) {
                        if (isDeleting) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                strokeWidth = 2.dp,
                                color = Color.White
                            )
                        } else {
                            Text("删除")
                        }
                    }
                },
                dismissButton = {
                    TextButton(
                        onClick = { showDeleteDialog = false },
                        enabled = !isDeleting
                    ) {
                        Text("取消")
                    }
                }
            )
        }
    }
}

fun retryUpload(context: Context, task: UploadTask) {
    val scope = (context as? MainActivity)?.lifecycleScope ?: CoroutineScope(Dispatchers.IO)
    scope.launch {
        try {
            Logger.upload("Retrying upload for file: ${task.originalFilename}")

            val token = context.dataStore.data.map { it[TOKEN_KEY] ?: "" }.first()
            if (token.isBlank()) {
                withContext(Dispatchers.Main) {
                    ErrorHandler.handleUploadError(
                        context,
                        SecurityException("用户未登录"),
                        task.originalFilename
                    )
                }
                return@launch
            }

            val workManager = WorkManager.getInstance(context)
            val database = AppDatabase.getDatabase(context)
            val uploadTaskDao = database.uploadTaskDao()

            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build()

            val uploadWorkRequest = OneTimeWorkRequestBuilder<UploadWorker>()
                .setConstraints(constraints)
                .setInputData(workDataOf(
                    UploadWorker.KEY_PHOTO_URI to task.photoUri,
                    UploadWorker.KEY_ORIGINAL_FILENAME to task.originalFilename,
                    UploadWorker.KEY_TOKEN to token,
                    UploadWorker.KEY_COMPANY_NAME to task.companyName
                ))
                .build()

            val newTask = task.copy(workRequestId = uploadWorkRequest.id, status = "PENDING")
            uploadTaskDao.updateTask(newTask)
            workManager.enqueue(uploadWorkRequest)

            Logger.upload("Upload retry scheduled for file: ${task.originalFilename}")
            withContext(Dispatchers.Main) {
                Toast.makeText(context, "已重新加入上传队列", Toast.LENGTH_SHORT).show()
            }

        } catch (e: Exception) {
            Logger.e("Failed to retry upload for file: ${task.originalFilename}", e)
            ErrorHandler.handleUploadError(context, e, task.originalFilename)
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun FullScreenImageScreen(navController: NavController, initialPage: Int) {
    val context = LocalContext.current
    val database = AppDatabase.getDatabase(context)
    val uploadTaskDao = database.uploadTaskDao()
    val tasks by uploadTaskDao.getAllTasks().observeAsState(initial = emptyList())

    val pagerState = rememberPagerState(initialPage = initialPage) {
        tasks.size
    }

    Box(modifier = Modifier.fillMaxSize()) {
        if (tasks.isNotEmpty()) {
            HorizontalPager(state = pagerState, modifier = Modifier.fillMaxSize()) { page ->
                AsyncImage(
                    model = Uri.parse(tasks[page].photoUri),
                    contentDescription = "全屏图片",
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Fit
                )
            }
        }
        IconButton(
            onClick = { navController.popBackStack() },
            modifier = Modifier.align(Alignment.TopStart).padding(16.dp)
        ) {
            Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "返回", tint = Color.White)
        }
    }
}

fun takePhoto(
    context: Context,
    imageCapture: ImageCapture?,
    executor: Executor,
    addWatermark: Boolean,
    currentLocation: Location?,
    companyName: String,
    siteName: String,
    selectedType: String,
    remarkText: String,
    soundPool: SoundPool,
    shutterSoundId: Int,
    errorSoundId: Int,
    onImageCaptured: (Uri) -> Unit
) {
    if (imageCapture == null) {
        ErrorHandler.handleCameraError(context, RuntimeException("相机未准备好"))
        return
    }

    Logger.camera("Starting photo capture")
    val timeStamp = SimpleDateFormat("yyyy-MM-dd-HH-mm-ss-SSS", Locale.US)
        .format(System.currentTimeMillis())
    val finalCompanyName = if (companyName.isNotBlank()) companyName else "未设置"
    val finalSiteName = if (siteName.isNotBlank()) siteName else "未设置"
    val name = "$finalCompanyName-$finalSiteName-$selectedType-$timeStamp.webp"

    val callback = object : ImageCapture.OnImageCapturedCallback() {
        override fun onCaptureSuccess(image: ImageProxy) {
            Logger.camera("Image captured successfully, processing...")

            // 使用协程处理图片
            (context as? MainActivity)?.lifecycleScope?.launch {
                try {
                    Logger.camera("开始图片处理流程 - 文件名: $name")
                    Logger.camera("参数 - 企业: $companyName, 站点: $siteName, 类型: $selectedType, 备注: $remarkText")

                    val processedBitmap = ImageProcessor.processImage(
                        imageProxy = image,
                        location = currentLocation,
                        companyName = companyName,
                        siteName = siteName,
                        addWatermark = addWatermark,
                        remarkText = remarkText
                    )

                    if (processedBitmap == null) {
                        Logger.e("图片处理返回null")
                        ErrorHandler.handleCameraError(context, RuntimeException("图片处理失败"))
                        soundPool.play(errorSoundId, 1f, 1f, 0, 0, 1f)
                        return@launch
                    }

                    Logger.camera("图片处理成功，开始保存到相册")
                    // 保存图片
                    val uri = saveImageToGallery(context, processedBitmap, name)
                    if (uri != null) {
                        Logger.camera("图片保存成功，URI: $uri")
                        onImageCaptured(uri)
                        soundPool.play(shutterSoundId, 1f, 1f, 0, 0, 1f)

                        Logger.upload("开始调度上传任务")
                        scheduleUpload(context, uri, name, companyName)
                        Logger.camera("Photo saved and upload scheduled")
                    } else {
                        Logger.e("图片保存失败，saveImageToGallery返回null")
                        ErrorHandler.handleCameraError(context, RuntimeException("保存图片失败"))
                        soundPool.play(errorSoundId, 1f, 1f, 0, 0, 1f)
                    }

                } catch (e: Exception) {
                    Logger.e("拍照处理过程中发生异常", e)
                    ErrorHandler.handleCameraError(context, e)
                    soundPool.play(errorSoundId, 1f, 1f, 0, 0, 1f)
                } finally {
                    image.close()
                }
            }
        }

        override fun onError(exception: ImageCaptureException) {
            Logger.e("Image capture failed", exception)
            ErrorHandler.handleCameraError(context, exception)
            soundPool.play(errorSoundId, 1f, 1f, 0, 0, 1f)
        }
    }

    imageCapture.takePicture(executor, callback)
}

/**
 * 保存图片到相册
 */
private suspend fun saveImageToGallery(
    context: Context,
    bitmap: Bitmap,
    fileName: String
): Uri? = withContext(Dispatchers.IO) {
    return@withContext ErrorHandler.safeExecute(
        context = context,
        operation = "保存图片",
        showToast = false
    ) {
        Logger.camera("开始保存图片到相册 - 文件名: $fileName")
        Logger.camera("图片尺寸: ${bitmap.width}x${bitmap.height}")

        val contentValues = ContentValues().apply {
            put(MediaStore.MediaColumns.DISPLAY_NAME, fileName)
            put(MediaStore.MediaColumns.MIME_TYPE, "image/webp")
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                put(MediaStore.Images.Media.RELATIVE_PATH, "Pictures/绿佳环境")
            }
        }

        Logger.camera("创建MediaStore URI")
        val uri = context.contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)

        if (uri == null) {
            Logger.e("MediaStore.insert返回null")
            return@safeExecute null
        }

        Logger.camera("MediaStore URI创建成功: $uri")

        uri.let { imageUri ->
            Logger.camera("开始写入图片数据")
            context.contentResolver.openOutputStream(imageUri)?.use { outputStream ->
                val success = bitmap.compress(Bitmap.CompressFormat.WEBP, 80, outputStream)
                Logger.camera("图片压缩写入${if (success) "成功" else "失败"}")
                if (!success) {
                    Logger.e("bitmap.compress返回false")
                    return@safeExecute null
                }
            } ?: run {
                Logger.e("无法打开输出流")
                return@safeExecute null
            }
            Logger.camera("图片保存完成: $imageUri")
            imageUri
        }
    }
}

fun scheduleUpload(context: Context, imageUri: Uri, fileName: String, companyName: String) {
    val scope = (context as? MainActivity)?.lifecycleScope ?: CoroutineScope(Dispatchers.IO)
    scope.launch {
        try {
            Logger.upload("开始调度上传任务 - 文件: $fileName, URI: $imageUri")

            val token = context.dataStore.data.map { it[TOKEN_KEY] ?: "" }.first()
            Logger.upload("获取到token: ${if (token.isBlank()) "空" else "有效(长度${token.length})"}")

            if (token.isBlank()) {
                Logger.e("用户未登录，无法上传")
                withContext(Dispatchers.Main) {
                    ErrorHandler.handleUploadError(
                        context,
                        SecurityException("用户未登录"),
                        fileName
                    )
                }
                return@launch
            }

            Logger.upload("初始化WorkManager和数据库")
            val workManager = WorkManager.getInstance(context)
            val database = AppDatabase.getDatabase(context)
            val uploadTaskDao = database.uploadTaskDao()

            // 防止同一图片重复上传
            Logger.upload("检查是否已存在上传任务")
            val existingTasks = uploadTaskDao.getTasksByPhotoUri(imageUri.toString())
            if (existingTasks.any { it.status != "FAILED" }) {
                Logger.upload("图片已在上传队列中: $fileName")
                withContext(Dispatchers.Main) {
                    Toast.makeText(context, "该图片已在上传队列中", Toast.LENGTH_SHORT).show()
                }
                return@launch
            }

            Logger.upload("创建上传约束条件")
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build()

            Logger.upload("创建WorkRequest")
            val uploadWorkRequest = OneTimeWorkRequestBuilder<UploadWorker>()
                .setConstraints(constraints)
                .setInputData(workDataOf(
                    UploadWorker.KEY_PHOTO_URI to imageUri.toString(),
                    UploadWorker.KEY_ORIGINAL_FILENAME to fileName,
                    UploadWorker.KEY_TOKEN to token,
                    UploadWorker.KEY_COMPANY_NAME to companyName
                ))
                .build()

            Logger.upload("创建上传任务记录，WorkRequest ID: ${uploadWorkRequest.id}")
            val task = UploadTask.create(
                photoUri = imageUri.toString(),
                workRequestId = uploadWorkRequest.id,
                originalFilename = fileName,
                companyName = companyName,
                status = "PENDING"
            )

            Logger.upload("插入任务到数据库")
            uploadTaskDao.insertTask(task)

            Logger.upload("将任务加入WorkManager队列")
            workManager.enqueue(uploadWorkRequest)

            Logger.upload("上传任务调度成功 - 文件: $fileName, WorkRequest ID: ${uploadWorkRequest.id}")
            withContext(Dispatchers.Main) {
                Toast.makeText(context, "图片已加入上传队列", Toast.LENGTH_SHORT).show()
            }

        } catch (e: Exception) {
            Logger.e("Failed to schedule upload for file: $fileName", e)
            ErrorHandler.handleUploadError(context, e, fileName)
        }
    }
}





@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddSiteScreen(
    navController: NavController,
    currentLocation: String,
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    var companyName by remember { mutableStateOf("") }
    var siteName by remember { mutableStateOf("") }

    Column(modifier = Modifier.fillMaxSize()) {
        TopAppBar(title = { Text("添加企业站点") }, navigationIcon = {
            IconButton(onClick = { navController.popBackStack() }) {
                Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "返回")
            }
        })

        Column(modifier = Modifier.padding(16.dp)) {
            TextField(
                value = companyName,
                onValueChange = { companyName = it },
                label = { Text("企业名") },
                modifier = Modifier.fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(16.dp))
            TextField(
                value = siteName,
                onValueChange = { siteName = it },
                label = { Text("站点名") },
                modifier = Modifier.fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text("当前GPS坐标: $currentLocation")
            Spacer(modifier = Modifier.height(16.dp))
            Button(
                onClick = {
                    if (companyName.isBlank() || siteName.isBlank()) {
                        Toast.makeText(context, "企业名和站点名不能为空", Toast.LENGTH_SHORT).show()
                        return@Button
                    }
                    scope.launch {
                        try {
                            Logger.network("Adding new site: $companyName - $siteName")

                            val token = context.dataStore.data.map { it[TOKEN_KEY] ?: "" }.first()
                            if (token.isBlank()) {
                                ErrorHandler.handleNetworkError(
                                    context,
                                    SecurityException("Token 不存在，无法添加")
                                )
                                return@launch
                            }

                            val client = OkHttpClient()
                            val requestBody = MultipartBody.Builder()
                                .setType(MultipartBody.FORM)
                                .addFormDataPart("EnterpriseName", companyName)
                                .addFormDataPart("SubName", siteName)
                                .addFormDataPart("Location", currentLocation)
                                .build()

                            val request = Request.Builder()
                                .url("https://lj.du1.net/api/qyxx/add")
                                .header("Token", token)
                                .post(requestBody)
                                .build()

                            val response = withContext(Dispatchers.IO) {
                                client.newCall(request).execute()
                            }

                            val responseBody = response.body?.string()
                            if (response.isSuccessful && responseBody != null) {
                                val jsonObject = JSONObject(responseBody)
                                if (jsonObject.optInt("code") == 1) {
                                    Logger.network("Site added successfully")
                                    withContext(Dispatchers.Main) {
                                        Toast.makeText(context, "添加成功", Toast.LENGTH_SHORT).show()
                                        navController.popBackStack()
                                    }
                                } else {
                                    val errorMsg = jsonObject.optString("msg", "未知错误")
                                    Logger.e("Failed to add site: $errorMsg")
                                    withContext(Dispatchers.Main) {
                                        Toast.makeText(context, "添加失败: $errorMsg", Toast.LENGTH_SHORT).show()
                                    }
                                }
                            } else {
                                Logger.e("HTTP error when adding site: ${response.code} ${response.message}")
                                withContext(Dispatchers.Main) {
                                    Toast.makeText(context, "添加失败: ${response.code} ${response.message}", Toast.LENGTH_SHORT).show()
                                }
                            }
                        } catch (e: Exception) {
                            Logger.e("Exception when adding site", e)
                            ErrorHandler.handleNetworkError(context, e, "添加站点失败")
                        }
                    }
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("确认添加")
            }
        }
    }
}