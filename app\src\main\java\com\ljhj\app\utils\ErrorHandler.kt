package com.ljhj.app.utils

import android.content.Context
import android.widget.Toast
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

/**
 * 统一异常处理工具
 */
object ErrorHandler {
    
    /**
     * 处理网络异常
     */
    fun handleNetworkError(
        context: Context,
        throwable: Throwable,
        customMessage: String? = null,
        showToast: Boolean = true
    ): String {
        val errorMessage = when (throwable) {
            is UnknownHostException -> "网络连接失败，请检查网络设置"
            is SocketTimeoutException -> "网络请求超时，请稍后重试"
            is IOException -> "网络异常，请检查网络连接"
            else -> customMessage ?: "未知网络错误：${throwable.message}"
        }
        
        Logger.e("Network error: $errorMessage", throwable)
        
        if (showToast) {
            showErrorToast(context, errorMessage)
        }
        
        return errorMessage
    }
    
    /**
     * 处理位置服务异常
     */
    fun handleLocationError(
        context: Context,
        throwable: Throwable,
        showToast: Boolean = true
    ): String {
        val errorMessage = when (throwable) {
            is SecurityException -> ErrorMessages.Location.PERMISSION_DENIED
            else -> ErrorMessages.getFriendlyMessage(throwable)
        }

        Logger.location("Location error: $errorMessage")
        Logger.e("Location error details", throwable)

        if (showToast) {
            val suggestion = ErrorMessages.getActionSuggestion(throwable)
            val fullMessage = if (suggestion != null) "$errorMessage\n$suggestion" else errorMessage
            showErrorToast(context, fullMessage)
        }

        return errorMessage
    }
    
    /**
     * 处理相机异常
     */
    fun handleCameraError(
        context: Context,
        throwable: Throwable,
        showToast: Boolean = true
    ): String {
        val errorMessage = when (throwable) {
            is SecurityException -> ErrorMessages.Camera.PERMISSION_DENIED
            is OutOfMemoryError -> ErrorMessages.Memory.OUT_OF_MEMORY
            else -> ErrorMessages.getFriendlyMessage(throwable)
        }

        Logger.camera("Camera error: $errorMessage")
        Logger.e("Camera error details", throwable)

        if (showToast) {
            val suggestion = ErrorMessages.getActionSuggestion(throwable)
            val fullMessage = if (suggestion != null) "$errorMessage\n$suggestion" else errorMessage
            showErrorToast(context, fullMessage)
        }

        return errorMessage
    }
    
    /**
     * 处理上传异常
     */
    fun handleUploadError(
        context: Context,
        throwable: Throwable,
        fileName: String? = null,
        showToast: Boolean = true
    ): String {
        val fileInfo = fileName?.let { " (文件: $it)" } ?: ""
        val baseMessage = when (throwable) {
            is IOException -> ErrorMessages.Network.UPLOAD_FAILED
            is SecurityException -> ErrorMessages.Auth.UNAUTHORIZED
            is OutOfMemoryError -> ErrorMessages.Memory.OUT_OF_MEMORY
            else -> ErrorMessages.getFriendlyMessage(throwable)
        }
        val errorMessage = "$baseMessage$fileInfo"

        Logger.upload("Upload error: $errorMessage")
        Logger.e("Upload error details", throwable)

        if (showToast) {
            val suggestion = ErrorMessages.getActionSuggestion(throwable)
            val fullMessage = if (suggestion != null) "$errorMessage\n$suggestion" else errorMessage
            showErrorToast(context, fullMessage)
        }

        return errorMessage
    }
    
    /**
     * 处理通用异常
     */
    fun handleGenericError(
        context: Context,
        throwable: Throwable,
        operation: String = "操作",
        showToast: Boolean = true
    ): String {
        val errorMessage = "${operation}失败：${throwable.message ?: "未知错误"}"
        
        Logger.e("Generic error in $operation", throwable)
        
        if (showToast) {
            showErrorToast(context, errorMessage)
        }
        
        return errorMessage
    }
    
    /**
     * 显示错误Toast
     */
    private fun showErrorToast(context: Context, message: String) {
        CoroutineScope(Dispatchers.Main).launch {
            Toast.makeText(context, message, Toast.LENGTH_LONG).show()
        }
    }
    
    /**
     * 安全执行代码块
     */
    inline fun <T> safeExecute(
        context: Context? = null,
        operation: String = "操作",
        showToast: Boolean = true,
        onError: (String) -> Unit = {},
        block: () -> T
    ): T? {
        return try {
            block()
        } catch (e: Exception) {
            val errorMessage = if (context != null) {
                handleGenericError(context, e, operation, showToast)
            } else {
                Logger.e("Error in $operation", e)
                "${operation}失败：${e.message}"
            }
            onError(errorMessage)
            null
        }
    }
}
