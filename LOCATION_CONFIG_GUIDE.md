# 站点距离关径配置指南

## 📍 配置文件位置

所有位置相关的配置现在统一在：
**`app/src/main/java/com/ljhj/app/config/LocationConfig.kt`**

## 🔧 主要配置参数

### 1. 站点搜索配置 (SiteSearch)

```kotlin
object SiteSearch {
    /** 搜索附近站点的半径（米） */
    const val SEARCH_RADIUS = 500
    
    /** 显示站点距离的单位阈值（米） */
    const val DISTANCE_DISPLAY_THRESHOLD = 1000
    
    /** 自动选择站点的最大距离（米） */
    const val AUTO_SELECT_MAX_DISTANCE = 50
}
```

**说明**：
- `SEARCH_RADIUS`：**主要配置项**，控制搜索附近站点的范围
- `DISTANCE_DISPLAY_THRESHOLD`：超过1000米显示为公里
- `AUTO_SELECT_MAX_DISTANCE`：距离很近时自动选择站点

### 2. GPS精度配置 (GpsAccuracy)

```kotlin
object GpsAccuracy {
    /** GPS定位精度接受阈值（米） */
    const val ACCURACY_THRESHOLD = 300f
    
    /** 高精度GPS阈值（米） */
    const val HIGH_ACCURACY_THRESHOLD = 50f
    
    /** 可接受的GPS精度阈值（米） */
    const val ACCEPTABLE_ACCURACY_THRESHOLD = 100f
    
    /** GPS定位超时时间（毫秒） */
    const val GPS_TIMEOUT_MS = 20000L
    
    /** Google Play Services 超时时间（毫秒） */
    const val GOOGLE_SERVICES_TIMEOUT_MS = 8000L
}
```

**说明**：
- `ACCURACY_THRESHOLD`：GPS精度接受阈值，超过此值会等待更精确的位置
- `GOOGLE_SERVICES_TIMEOUT_MS`：Google服务超时时间，防止卡住

### 3. 位置更新配置 (LocationUpdate)

```kotlin
object LocationUpdate {
    /** 位置更新间隔（毫秒） */
    const val UPDATE_INTERVAL_MS = 5000L
    
    /** 最小更新间隔（毫秒） */
    const val MIN_UPDATE_INTERVAL_MS = 2500L
    
    /** 最小位移距离（米） */
    const val MIN_DISTANCE_CHANGE = 5f
}
```

## 🎯 常用修改场景

### 场景1：修改站点搜索范围

如果您想修改搜索附近站点的距离范围：

```kotlin
// 在 LocationConfig.kt 中修改
const val SEARCH_RADIUS = 1000  // 改为1000米搜索范围
```

**影响**：
- 用户获取GPS后，会搜索1000米范围内的站点
- 距离更远的站点不会显示在选择列表中

### 场景2：修改GPS精度要求

如果您想调整GPS精度要求：

```kotlin
// 在 LocationConfig.kt 中修改
const val ACCURACY_THRESHOLD = 200f  // 改为200米精度要求
```

**影响**：
- GPS精度超过200米时会继续等待更精确的位置
- 精度要求越低，定位越快但可能不够准确

### 场景3：修改距离显示格式

如果您想调整距离显示的阈值：

```kotlin
// 在 LocationConfig.kt 中修改
const val DISTANCE_DISPLAY_THRESHOLD = 500  // 超过500米显示为公里
```

**影响**：
- 距离小于500米：显示为"XXX米"
- 距离大于500米：显示为"X.X公里"

## 📱 实际效果对比

### 不同搜索半径的效果

| 搜索半径 | 适用场景 | 优点 | 缺点 |
|---------|---------|------|------|
| 200米 | 密集区域 | 精确定位，减少选择 | 可能找不到站点 |
| 500米 | **推荐设置** | 平衡精度和覆盖 | 适中 |
| 1000米 | 稀疏区域 | 覆盖范围广 | 可能显示过多站点 |
| 2000米 | 偏远地区 | 最大覆盖 | 精度较低 |

### 不同GPS精度的效果

| 精度阈值 | 定位速度 | 位置准确性 | 适用场景 |
|---------|---------|-----------|---------|
| 100米 | 较慢 | 高精度 | 要求精确定位 |
| 300米 | **推荐** | 平衡 | 一般使用 |
| 500米 | 较快 | 中等精度 | 快速定位 |

## 🔄 配置修改步骤

### 1. 修改配置文件

编辑 `app/src/main/java/com/ljhj/app/config/LocationConfig.kt`：

```kotlin
object SiteSearch {
    const val SEARCH_RADIUS = 800  // 修改为您需要的值
}
```

### 2. 重新编译应用

```bash
./gradlew assembleDebug
```

### 3. 测试验证

- 安装新版本应用
- 测试GPS获取和站点搜索功能
- 验证距离显示是否符合预期

## 🧪 测试建议

### 测试不同距离的站点

1. **近距离测试**（<100米）
   - 验证是否自动选择站点
   - 检查距离显示格式

2. **中距离测试**（100-500米）
   - 验证站点列表显示
   - 检查距离排序

3. **远距离测试**（>1000米）
   - 验证是否显示为公里
   - 检查搜索范围限制

### 测试GPS精度

1. **室内测试**
   - 验证低精度GPS的处理
   - 检查超时机制

2. **室外测试**
   - 验证高精度GPS的快速响应
   - 检查位置更新频率

## 📊 监控和调试

### 查看配置生效情况

使用 `adb logcat` 查看日志：

```bash
adb logcat | grep "LJHJ_APP_LOCATION"
```

**关键日志**：
- `"Search radius: XXX meters"`
- `"GPS accuracy: XXX meters"`
- `"Found X sites within range"`

### 性能监控

关注以下指标：
- GPS获取时间
- 站点搜索响应时间
- 用户选择站点的频率

## 🚀 高级配置

### 动态配置（未来扩展）

可以考虑添加用户设置界面，让用户自定义：
- 搜索半径偏好
- GPS精度要求
- 距离显示单位

### 地区适配

不同地区可能需要不同的配置：
- 城市地区：较小搜索半径
- 农村地区：较大搜索半径
- 工业区：中等搜索半径

---

**总结**：通过统一的配置文件，您可以轻松调整站点距离关径和其他位置相关参数。建议在修改配置后进行充分测试，确保在不同环境下都能正常工作。
