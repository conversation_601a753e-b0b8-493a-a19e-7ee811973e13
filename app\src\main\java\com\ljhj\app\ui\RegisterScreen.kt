
package com.ljhj.app.ui

import androidx.compose.foundation.layout.*
import androidx.compose.material3.Button
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import android.widget.Toast
import androidx.compose.ui.platform.LocalContext
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RegisterScreen(navController: NavController) {
    var username by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var email by remember { mutableStateOf("") }
    var mobile by remember { mutableStateOf("") }
    var code by remember { mutableStateOf("") }
    var errorMessage by remember { mutableStateOf("") }
    var successMessage by remember { mutableStateOf("") }
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        TextField(
            value = username,
            onValueChange = { username = it },
            label = { Text("用户名") },
            modifier = Modifier.fillMaxWidth()
        )
        Spacer(modifier = Modifier.height(16.dp))
        TextField(
            value = password,
            onValueChange = { password = it },
            label = { Text("密码") },
            modifier = Modifier.fillMaxWidth()
        )
        Spacer(modifier = Modifier.height(16.dp))
        TextField(
            value = email,
            onValueChange = { email = it },
            label = { Text("邮箱") },
            modifier = Modifier.fillMaxWidth()
        )
        Spacer(modifier = Modifier.height(16.dp))
        TextField(
            value = mobile,
            onValueChange = { mobile = it },
            label = { Text("手机号") },
            modifier = Modifier.fillMaxWidth()
        )
        Spacer(modifier = Modifier.height(16.dp))
        Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
            TextField(
                value = code,
                onValueChange = { code = it },
                label = { Text("验证码") },
                modifier = Modifier.weight(1f)
            )
            Button(onClick = {
                coroutineScope.launch {
                    val result = sendVerificationCode(mobile)
                    if (result.isSuccess) {
                        Toast.makeText(context, "验证码发送成功", Toast.LENGTH_SHORT).show()
                    } else {
                        Toast.makeText(context, result.exceptionOrNull()?.message ?: "验证码发送失败", Toast.LENGTH_SHORT).show()
                    }
                }
            }) {
                Text("发送验证码")
            }
        }
        Spacer(modifier = Modifier.height(16.dp))
        Button(
            onClick = {
                coroutineScope.launch {
                    val result = registerUser(username, password, email, mobile, code)
                    if (result.isSuccess) {
                        successMessage = "注册成功"
                        errorMessage = ""
                        navController.popBackStack()
                    } else {
                        errorMessage = result.exceptionOrNull()?.message ?: "注册失败"
                        successMessage = ""
                    }
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("注册")
        }
        if (errorMessage.isNotEmpty()) {
            Text(
                text = errorMessage,
                color = Color.Red,
                modifier = Modifier.padding(top = 8.dp)
            )
        }
        if (successMessage.isNotEmpty()) {
            Text(
                text = successMessage,
                color = Color.Green,
                modifier = Modifier.padding(top = 8.dp)
            )
        }
    }
}

private suspend fun registerUser(username: String, password: String, email: String, mobile: String, code: String): Result<Unit> = withContext(Dispatchers.IO) {
    try {
        val url = URL("https://lj.du1.net/api/user/register")
        val connection = url.openConnection() as HttpURLConnection
        connection.requestMethod = "POST"
        connection.doOutput = true
        connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded")

        val postData = "username=$username&password=$password&email=$email&mobile=$mobile&code=$code"

        val outputStreamWriter = OutputStreamWriter(connection.outputStream)
        outputStreamWriter.write(postData)
        outputStreamWriter.flush()

        val responseCode = connection.responseCode
        if (responseCode == HttpURLConnection.HTTP_OK) {
            val reader = BufferedReader(InputStreamReader(connection.inputStream))
            val response = reader.readText()
            val jsonObject = JSONObject(response)
            val responseCode = jsonObject.getInt("code")
            if (responseCode == 1) {
                Result.success(Unit)
            } else {
                val msg = jsonObject.getString("msg")
                Result.failure(Exception(msg))
            }
        } else {
            Result.failure(Exception("注册失败，错误码: $responseCode"))
        }
    } catch (e: Exception) {
        Result.failure(Exception("注册失败: ${e.message}"))
    }
}

private suspend fun sendVerificationCode(mobile: String): Result<Unit> = withContext(Dispatchers.IO) {
    try {
        val url = URL("https://lj.du1.net/api/sms/send")
        val connection = url.openConnection() as HttpURLConnection
        connection.requestMethod = "POST"
        connection.doOutput = true
        connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded")

        val postData = "mobile=$mobile&event=register"

        val outputStreamWriter = OutputStreamWriter(connection.outputStream)
        outputStreamWriter.write(postData)
        outputStreamWriter.flush()

        val responseCode = connection.responseCode
        if (responseCode == HttpURLConnection.HTTP_OK) {
            val reader = BufferedReader(InputStreamReader(connection.inputStream))
            val response = reader.readText()
            val jsonObject = JSONObject(response)
            val code = jsonObject.getInt("code")
            if (code == 1) {
                Result.success(Unit)
            } else {
                val msg = jsonObject.getString("msg")
                Result.failure(Exception(msg))
            }
        } else {
            Result.failure(Exception("发送失败，错误码: $responseCode"))
        }
    } catch (e: Exception) {
        Result.failure(Exception("发送失败: ${e.message}"))
    }
}
