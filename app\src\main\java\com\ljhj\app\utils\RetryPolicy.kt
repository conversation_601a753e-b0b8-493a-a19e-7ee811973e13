package com.ljhj.app.utils

import kotlinx.coroutines.delay
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

/**
 * 智能重试策略
 */
data class RetryPolicy(
    val maxRetries: Int = 3,
    val baseDelayMs: Long = 1000L,
    val maxDelayMs: Long = 30000L,
    val backoffMultiplier: Double = 2.0,
    val retryableExceptions: Set<Class<out Exception>> = setOf(
        IOException::class.java,
        SocketTimeoutException::class.java,
        UnknownHostException::class.java
    )
) {
    
    /**
     * 计算延迟时间（指数退避）
     */
    fun calculateDelay(attempt: Int): Long {
        val delay = (baseDelayMs * Math.pow(backoffMultiplier, attempt.toDouble())).toLong()
        return minOf(delay, maxDelayMs)
    }
    
    /**
     * 判断异常是否可重试
     */
    fun isRetryable(exception: Throwable): Boolean {
        return retryableExceptions.any { it.isInstance(exception) }
    }
}

/**
 * 重试执行器
 */
object RetryExecutor {
    
    /**
     * 带重试的执行
     */
    suspend fun <T> executeWithRetry(
        retryPolicy: RetryPolicy = RetryPolicy(),
        operation: String = "操作",
        block: suspend (attempt: Int) -> T
    ): T {
        var lastException: Exception? = null
        
        repeat(retryPolicy.maxRetries + 1) { attempt ->
            try {
                Logger.d("Executing $operation, attempt: ${attempt + 1}")
                return block(attempt)
            } catch (e: Exception) {
                lastException = e
                
                if (attempt == retryPolicy.maxRetries) {
                    Logger.e("$operation failed after ${retryPolicy.maxRetries + 1} attempts", e)
                    throw e
                }
                
                if (!retryPolicy.isRetryable(e)) {
                    Logger.e("$operation failed with non-retryable exception", e)
                    throw e
                }
                
                val delayMs = retryPolicy.calculateDelay(attempt)
                Logger.w("$operation failed (attempt ${attempt + 1}), retrying in ${delayMs}ms", e)
                delay(delayMs)
            }
        }
        
        throw lastException ?: RuntimeException("Unknown error in retry execution")
    }
}

/**
 * 上传重试策略
 */
object UploadRetryPolicy {
    
    /**
     * 网络上传重试策略
     */
    val networkUpload = RetryPolicy(
        maxRetries = 3,
        baseDelayMs = 2000L,
        maxDelayMs = 60000L,
        backoffMultiplier = 2.0
    )
    
    /**
     * 文件读取重试策略
     */
    val fileRead = RetryPolicy(
        maxRetries = 2,
        baseDelayMs = 500L,
        maxDelayMs = 5000L,
        backoffMultiplier = 2.0
    )
    
    /**
     * 数据库操作重试策略
     */
    val database = RetryPolicy(
        maxRetries = 2,
        baseDelayMs = 100L,
        maxDelayMs = 1000L,
        backoffMultiplier = 2.0
    )
}
