--------- beginning of system
--------- beginning of crash
--------- beginning of main
2025-07-07 16:29:10.980  5663-5764  <PERSON><PERSON>                  pid-5663                             E  [73] ItemStore: getItems RPC failed for item com.ljhj.app
2025-07-07 17:01:53.829  5663-5705  <PERSON>sky                  pid-5663                             E  [58] ItemStore: getItems RPC failed for item com.ljhj.app
2025-07-07 17:01:53.830  5663-5735  <PERSON><PERSON>                  pid-5663                             E  [64] ItemStore: getItems RPC failed for item com.ljhj.app
2025-07-07 17:20:58.535  8679-8689  BufferQueueProducer     pid-8679                             E  [SurfaceView[com.ljhj.app/com.ljhj.app.MainActivity]#6(BLAST Consumer)6](id:21e700000007,api:4,p:479,c:8679) queueBuffer: BufferQueue has been abandoned
2025-07-07 17:20:58.536  8679-8707  BufferQueueProducer     pid-8679                             E  [SurfaceView[com.ljhj.app/com.ljhj.app.MainActivity]#6(BLAST Consumer)6](id:21e700000007,api:4,p:479,c:8679) dequeueBuffer: BufferQueue has been abandoned
2025-07-07 17:20:58.570  8679-8691  BufferQueueProducer     pid-8679                             E  [SurfaceView[com.ljhj.app/com.ljhj.app.MainActivity]#6(BLAST Consumer)6](id:21e700000007,api:4,p:479,c:8679) queueBuffer: BufferQueue has been abandoned
2025-07-07 17:20:58.598  8679-8691  BufferQueueProducer     pid-8679                             E  [SurfaceView[com.ljhj.app/com.ljhj.app.MainActivity]#6(BLAST Consumer)6](id:21e700000007,api:4,p:479,c:8679) queueBuffer: BufferQueue has been abandoned
2025-07-07 17:20:58.637  8679-8689  BufferQueueProducer     pid-8679                             E  [SurfaceView[com.ljhj.app/com.ljhj.app.MainActivity]#6(BLAST Consumer)6](id:21e700000007,api:4,p:479,c:8679) queueBuffer: BufferQueue has been abandoned
2025-07-07 17:54:33.360  9427-9427  com.ljhj.app            com.ljhj.app                         I  Late-enabling -Xcheck:jni
2025-07-07 17:54:34.056  9427-9427  com.ljhj.app            com.ljhj.app                         I  Using CollectorTypeCMC GC.
2025-07-07 17:54:34.074  9427-9427  com.ljhj.app            com.ljhj.app                         W  Unexpected CPU variant for x86: x86_64.
                                                                                                    Known variants: atom, sandybridge, silvermont, goldmont, goldmont-plus, goldmont-without-sha-xsaves, tremont, kabylake, default
2025-07-07 17:54:34.360  9427-9427  nativeloader            com.ljhj.app                         D  Load libframework-connectivity-tiramisu-jni.so using APEX ns com_android_tethering for caller /apex/com.android.tethering/javalib/framework-connectivity-t.jar: ok
2025-07-07 17:54:34.561  9427-9427  re-initialized>         com.ljhj.app                         W  type=1400 audit(0.0:775): avc:  granted  { execute } for  path="/data/data/com.ljhj.app/code_cache/startup_agents/9758b833-agent.so" dev="dm-46" ino=362603 scontext=u:r:untrusted_app:s0:c209,c256,c512,c768 tcontext=u:object_r:app_data_file:s0:c209,c256,c512,c768 tclass=file app=com.ljhj.app
2025-07-07 17:54:34.582  9427-9427  nativeloader            com.ljhj.app                         D  Load /data/user/0/com.ljhj.app/code_cache/startup_agents/9758b833-agent.so using system ns (caller=<unknown>): ok
2025-07-07 17:54:34.610  9427-9427  com.ljhj.app            com.ljhj.app                         W  DexFile /data/data/com.ljhj.app/code_cache/.studio/instruments-07dd17c6.jar is in boot class path but is not in a known location
2025-07-07 17:54:34.825  9427-9427  com.ljhj.app            com.ljhj.app                         W  Redefining intrinsic method java.lang.Thread java.lang.Thread.currentThread(). This may cause the unexpected use of the original definition of java.lang.Thread java.lang.Thread.currentThread()in methods that have already been compiled.
2025-07-07 17:54:34.825  9427-9427  com.ljhj.app            com.ljhj.app                         W  Redefining intrinsic method boolean java.lang.Thread.interrupted(). This may cause the unexpected use of the original definition of boolean java.lang.Thread.interrupted()in methods that have already been compiled.
2025-07-07 17:54:34.989  9427-9427  ziparchive              com.ljhj.app                         W  Unable to open '/data/data/com.ljhj.app/code_cache/.overlay/base.apk/classes3.dm': No such file or directory
2025-07-07 17:54:34.992  9427-9427  ziparchive              com.ljhj.app                         W  Unable to open '/data/data/com.ljhj.app/code_cache/.overlay/base.apk/classes4.dm': No such file or directory
2025-07-07 17:54:34.994  9427-9427  ziparchive              com.ljhj.app                         W  Unable to open '/data/app/~~gtHxtHjs8sRDYZO0S7SLUg==/com.ljhj.app-UA-zE0ayFnhZBosso2KRHg==/base.dm': No such file or directory
2025-07-07 17:54:34.994  9427-9427  ziparchive              com.ljhj.app                         W  Unable to open '/data/app/~~gtHxtHjs8sRDYZO0S7SLUg==/com.ljhj.app-UA-zE0ayFnhZBosso2KRHg==/base.dm': No such file or directory
2025-07-07 17:54:36.898  9427-9427  nativeloader            com.ljhj.app                         D  Configuring clns-7 for other apk /data/app/~~gtHxtHjs8sRDYZO0S7SLUg==/com.ljhj.app-UA-zE0ayFnhZBosso2KRHg==/base.apk. target_sdk_version=36, uses_libraries=, library_path=/data/app/~~gtHxtHjs8sRDYZO0S7SLUg==/com.ljhj.app-UA-zE0ayFnhZBosso2KRHg==/lib/x86_64:/data/app/~~gtHxtHjs8sRDYZO0S7SLUg==/com.ljhj.app-UA-zE0ayFnhZBosso2KRHg==/base.apk!/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.ljhj.app
2025-07-07 17:54:36.956  9427-9427  GraphicsEnvironment     com.ljhj.app                         V  Currently set values for:
2025-07-07 17:54:36.956  9427-9427  GraphicsEnvironment     com.ljhj.app                         V    angle_gl_driver_selection_pkgs=[]
2025-07-07 17:54:36.956  9427-9427  GraphicsEnvironment     com.ljhj.app                         V    angle_gl_driver_selection_values=[]
2025-07-07 17:54:36.956  9427-9427  GraphicsEnvironment     com.ljhj.app                         V  Global.Settings values are invalid: number of packages: 0, number of values: 0
2025-07-07 17:54:36.956  9427-9427  GraphicsEnvironment     com.ljhj.app                         V  Neither updatable production driver nor prerelease driver is supported.
2025-07-07 17:54:37.353  9427-9446  MediaCodecList          com.ljhj.app                         D  codecHandlesFormat: no format, so no extra checks
2025-07-07 17:54:37.410  9427-9447  CCodec                  com.ljhj.app                         D  allocate(c2.android.raw.decoder)
2025-07-07 17:54:37.464  9427-9427  HWUI                    com.ljhj.app                         W  Unknown dataspace 0
2025-07-07 17:54:37.481  9427-9447  Codec2Client            com.ljhj.app                         I  Available Codec2 services: "default" "software"
2025-07-07 17:54:37.537  9427-9447  CCodec                  com.ljhj.app                         I  setting up 'default' as default (vendor) store
2025-07-07 17:54:37.656  9427-9447  CCodec                  com.ljhj.app                         I  Created component [c2.android.raw.decoder]
2025-07-07 17:54:37.659  9427-9447  CCodecConfig            com.ljhj.app                         D  read media type: audio/raw
2025-07-07 17:54:37.664  9427-9447  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: algo.buffers.max-count.values
2025-07-07 17:54:37.665  9427-9447  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: output.subscribed-indices.values
2025-07-07 17:54:37.665  9427-9447  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: input.buffers.allocator-ids.values
2025-07-07 17:54:37.665  9427-9447  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: output.buffers.allocator-ids.values
2025-07-07 17:54:37.665  9427-9447  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: algo.buffers.allocator-ids.values
2025-07-07 17:54:37.666  9427-9447  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: output.buffers.pool-ids.values
2025-07-07 17:54:37.666  9427-9447  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: algo.buffers.pool-ids.values
2025-07-07 17:54:37.685  9427-9447  CCodecConfig            com.ljhj.app                         I  query failed after returning 9 values (BAD_INDEX)
2025-07-07 17:54:37.685  9427-9447  CCodecConfig            com.ljhj.app                         D  c2 config diff is Dict {
                                                                                                      c2::u32 coded.bitrate.value = 64000
                                                                                                      c2::u32 input.buffers.max-size.value = 65536
                                                                                                      c2::u32 input.delay.value = 0
                                                                                                      string input.media-type.value = "audio/raw"
                                                                                                      c2::u32 output.large-frame.max-size = 0
                                                                                                      c2::u32 output.large-frame.threshold-size = 0
                                                                                                      string output.media-type.value = "audio/raw"
                                                                                                      c2::u32 raw.channel-count.value = 2
                                                                                                      c2::u32 raw.pcm-encoding.value = 0
                                                                                                      c2::u32 raw.sample-rate.value = 44100
                                                                                                    }
2025-07-07 17:54:37.694  9427-9447  CCodec                  com.ljhj.app                         D  [c2.android.raw.decoder] buffers are bound to CCodec for this session
2025-07-07 17:54:37.694  9427-9447  CCodecConfig            com.ljhj.app                         D  no c2 equivalents for durationUs
2025-07-07 17:54:37.694  9427-9447  CCodecConfig            com.ljhj.app                         D  no c2 equivalents for track-id
2025-07-07 17:54:37.694  9427-9447  CCodecConfig            com.ljhj.app                         D  no c2 equivalents for bits-per-sample
2025-07-07 17:54:37.694  9427-9447  CCodecConfig            com.ljhj.app                         D  no c2 equivalents for flags
2025-07-07 17:54:37.695  9427-9447  Codec2Client            com.ljhj.app                         W  query -- param skipped: index = 1107298332.
2025-07-07 17:54:37.695  9427-9447  CCodec                  com.ljhj.app                         D  encoding statistics level = 0
2025-07-07 17:54:37.695  9427-9447  CCodec                  com.ljhj.app                         D  setup formats input: AMessage(what = 0x00000000) = {
                                                                                                      int32_t android._codec-pcm-encoding = 2
                                                                                                      int32_t bitrate = 64000
                                                                                                      int32_t channel-count = 2
                                                                                                      int32_t max-input-size = 65536
                                                                                                      string mime = "audio/raw"
                                                                                                      int32_t pcm-encoding = 2
                                                                                                      int32_t sample-rate = 44100
                                                                                                    }
2025-07-07 17:54:37.695  9427-9447  CCodec                  com.ljhj.app                         D  setup formats output: AMessage(what = 0x00000000) = {
                                                                                                      int32_t android._codec-pcm-encoding = 2
                                                                                                      int32_t buffer-batch-max-output-size = 0
                                                                                                      int32_t buffer-batch-threshold-output-size = 0
                                                                                                      int32_t channel-count = 2
                                                                                                      string mime = "audio/raw"
                                                                                                      int32_t pcm-encoding = 2
                                                                                                      int32_t sample-rate = 44100
                                                                                                      int32_t channel-mask = 0
                                                                                                      int32_t android._config-pcm-encoding = 2
                                                                                                    }
2025-07-07 17:54:37.696  9427-9447  CCodecConfig            com.ljhj.app                         I  query failed after returning 9 values (BAD_INDEX)
2025-07-07 17:54:37.698  9427-9447  Codec2Client            com.ljhj.app                         W  query -- param skipped: index = 1342179345.
2025-07-07 17:54:37.698  9427-9447  Codec2Client            com.ljhj.app                         W  query -- param skipped: index = 2415921170.
2025-07-07 17:54:37.701  9427-9447  C2Store                 com.ljhj.app                         D  Using DMABUF Heaps
2025-07-07 17:54:37.709  9427-9447  CCodecBufferChannel     com.ljhj.app                         D  [c2.android.raw.decoder#936] Created input block pool with allocatorID 16 => poolID 17 - OK (0)
2025-07-07 17:54:37.710  9427-9447  CCodecBufferChannel     com.ljhj.app                         I  [c2.android.raw.decoder#936] Created output block pool with allocatorID 16 => poolID 78 - OK
2025-07-07 17:54:37.710  9427-9447  CCodecBufferChannel     com.ljhj.app                         D  [c2.android.raw.decoder#936] Configured output block pool ids 78 => OK
2025-07-07 17:54:37.723  9427-9447  DMABUFHEAPS             com.ljhj.app                         I  Using DMA-BUF heap named: system
2025-07-07 17:54:37.789  9427-9447  CCodecBufferChannel     com.ljhj.app                         D  [c2.android.raw.decoder#936] MediaCodec discarded an unknown buffer
2025-07-07 17:54:37.789  9427-9447  CCodecBufferChannel     com.ljhj.app                         D  [c2.android.raw.decoder#936] MediaCodec discarded an unknown buffer
2025-07-07 17:54:37.789  9427-9447  CCodecBufferChannel     com.ljhj.app                         D  [c2.android.raw.decoder#936] MediaCodec discarded an unknown buffer
2025-07-07 17:54:37.794  9427-9447  hw-BpHwBinder           com.ljhj.app                         I  onLastStrongRef automatically unlinking death recipients
2025-07-07 17:54:37.816  9427-9446  MediaCodecList          com.ljhj.app                         D  codecHandlesFormat: no format, so no extra checks
2025-07-07 17:54:37.816  9427-9454  CCodec                  com.ljhj.app                         D  allocate(c2.android.raw.decoder)
2025-07-07 17:54:37.818  9427-9454  CCodec                  com.ljhj.app                         I  setting up 'default' as default (vendor) store
2025-07-07 17:54:37.821  9427-9454  CCodec                  com.ljhj.app                         I  Created component [c2.android.raw.decoder]
2025-07-07 17:54:37.821  9427-9454  CCodecConfig            com.ljhj.app                         D  read media type: audio/raw
2025-07-07 17:54:37.825  9427-9454  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: algo.buffers.max-count.values
2025-07-07 17:54:37.826  9427-9454  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: output.subscribed-indices.values
2025-07-07 17:54:37.827  9427-9454  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: input.buffers.allocator-ids.values
2025-07-07 17:54:37.827  9427-9454  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: output.buffers.allocator-ids.values
2025-07-07 17:54:37.827  9427-9454  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: algo.buffers.allocator-ids.values
2025-07-07 17:54:37.827  9427-9454  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: output.buffers.pool-ids.values
2025-07-07 17:54:37.828  9427-9454  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: algo.buffers.pool-ids.values
2025-07-07 17:54:37.835  9427-9454  CCodecConfig            com.ljhj.app                         I  query failed after returning 9 values (BAD_INDEX)
2025-07-07 17:54:37.835  9427-9454  CCodecConfig            com.ljhj.app                         D  c2 config diff is Dict {
                                                                                                      c2::u32 coded.bitrate.value = 64000
                                                                                                      c2::u32 input.buffers.max-size.value = 65536
                                                                                                      c2::u32 input.delay.value = 0
                                                                                                      string input.media-type.value = "audio/raw"
                                                                                                      c2::u32 output.large-frame.max-size = 0
                                                                                                      c2::u32 output.large-frame.threshold-size = 0
                                                                                                      string output.media-type.value = "audio/raw"
                                                                                                      c2::u32 raw.channel-count.value = 2
                                                                                                      c2::u32 raw.pcm-encoding.value = 0
                                                                                                      c2::u32 raw.sample-rate.value = 44100
                                                                                                    }
2025-07-07 17:54:37.837  9427-9454  CCodec                  com.ljhj.app                         D  [c2.android.raw.decoder] buffers are bound to CCodec for this session
2025-07-07 17:54:37.837  9427-9454  CCodecConfig            com.ljhj.app                         D  no c2 equivalents for durationUs
2025-07-07 17:54:37.837  9427-9454  CCodecConfig            com.ljhj.app                         D  no c2 equivalents for track-id
2025-07-07 17:54:37.837  9427-9454  CCodecConfig            com.ljhj.app                         D  no c2 equivalents for bits-per-sample
2025-07-07 17:54:37.837  9427-9454  CCodecConfig            com.ljhj.app                         D  no c2 equivalents for flags
2025-07-07 17:54:37.839  9427-9454  CCodecConfig            com.ljhj.app                         D  c2 config diff is   c2::u32 raw.sample-rate.value = 11025
2025-07-07 17:54:37.840  9427-9454  Codec2Client            com.ljhj.app                         W  query -- param skipped: index = 1107298332.
2025-07-07 17:54:37.840  9427-9454  CCodec                  com.ljhj.app                         D  encoding statistics level = 0
2025-07-07 17:54:37.840  9427-9454  CCodec                  com.ljhj.app                         D  setup formats input: AMessage(what = 0x00000000) = {
                                                                                                      int32_t android._codec-pcm-encoding = 2
                                                                                                      int32_t bitrate = 64000
                                                                                                      int32_t channel-count = 2
                                                                                                      int32_t max-input-size = 65536
                                                                                                      string mime = "audio/raw"
                                                                                                      int32_t pcm-encoding = 2
                                                                                                      int32_t sample-rate = 11025
                                                                                                    }
2025-07-07 17:54:37.840  9427-9454  CCodec                  com.ljhj.app                         D  setup formats output: AMessage(what = 0x00000000) = {
                                                                                                      int32_t android._codec-pcm-encoding = 2
                                                                                                      int32_t buffer-batch-max-output-size = 0
                                                                                                      int32_t buffer-batch-threshold-output-size = 0
                                                                                                      int32_t channel-count = 2
                                                                                                      string mime = "audio/raw"
                                                                                                      int32_t pcm-encoding = 2
                                                                                                      int32_t sample-rate = 11025
                                                                                                      int32_t channel-mask = 0
                                                                                                      int32_t android._config-pcm-encoding = 2
                                                                                                    }
2025-07-07 17:54:37.840  9427-9454  CCodecConfig            com.ljhj.app                         I  query failed after returning 9 values (BAD_INDEX)
2025-07-07 17:54:37.842  9427-9454  Codec2Client            com.ljhj.app                         W  query -- param skipped: index = 1342179345.
2025-07-07 17:54:37.842  9427-9454  Codec2Client            com.ljhj.app                         W  query -- param skipped: index = 2415921170.
2025-07-07 17:54:37.846  9427-9454  CCodecBufferChannel     com.ljhj.app                         D  [c2.android.raw.decoder#575] Created input block pool with allocatorID 16 => poolID 18 - OK (0)
2025-07-07 17:54:37.848  9427-9454  CCodecBufferChannel     com.ljhj.app                         I  [c2.android.raw.decoder#575] Created output block pool with allocatorID 16 => poolID 80 - OK
2025-07-07 17:54:37.849  9427-9454  CCodecBufferChannel     com.ljhj.app                         D  [c2.android.raw.decoder#575] Configured output block pool ids 80 => OK
2025-07-07 17:54:37.861  9427-9454  CCodecBufferChannel     com.ljhj.app                         D  [c2.android.raw.decoder#575] MediaCodec discarded an unknown buffer
2025-07-07 17:54:37.862  9427-9454  CCodecBufferChannel     com.ljhj.app                         D  [c2.android.raw.decoder#575] MediaCodec discarded an unknown buffer
2025-07-07 17:54:37.862  9427-9454  CCodecBufferChannel     com.ljhj.app                         D  [c2.android.raw.decoder#575] MediaCodec discarded an unknown buffer
2025-07-07 17:54:37.864  9427-9454  hw-BpHwBinder           com.ljhj.app                         I  onLastStrongRef automatically unlinking death recipients
2025-07-07 17:54:37.881  9427-9432  com.ljhj.app            com.ljhj.app                         I  Compiler allocated 5174KB to compile void android.view.ViewRootImpl.performTraversals()
2025-07-07 17:54:39.281  9427-9442  EGL_emulation           com.ljhj.app                         I  Opening libGLESv1_CM_emulation.so
2025-07-07 17:54:39.288  9427-9442  EGL_emulation           com.ljhj.app                         I  Opening libGLESv2_emulation.so
2025-07-07 17:54:39.347  9427-9432  com.ljhj.app            com.ljhj.app                         I  Compiler allocated 5417KB to compile void android.view.ViewRootImpl.performTraversals()
2025-07-07 17:54:39.417  9427-9442  HWUI                    com.ljhj.app                         W  Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
2025-07-07 17:54:39.417  9427-9442  HWUI                    com.ljhj.app                         W  Failed to initialize 101010-2 format, error = EGL_SUCCESS
2025-07-07 17:54:39.511  9427-9442  Gralloc4                com.ljhj.app                         I  mapper 4.x is not supported
2025-07-07 17:54:39.746  9427-9438  HWUI                    com.ljhj.app                         I  Davey! duration=2237ms; Flags=1, FrameTimelineVsyncId=247834, IntendedVsync=4591718443444, Vsync=4591718443444, InputEventId=0, HandleInputStart=4591720955120, AnimationStart=4591720983720, PerformTraversalsStart=4591721025420, DrawStart=4593818607820, FrameDeadline=4591735110110, FrameInterval=4591720927720, FrameStartTime=16666666, SyncQueued=4593869653820, SyncStart=4593871193620, IssueDrawCommandsStart=4593873853120, SwapBuffers=4593944041020, FrameCompleted=4593957813420, DequeueBufferDuration=56400, QueueBufferDuration=1461500, GpuCompleted=4593957813420, SwapBuffersCompleted=4593948084120, DisplayPresentTime=72904454231359566, CommandSubmissionCompleted=4593944041020, 
2025-07-07 17:54:39.749  9427-9427  Choreographer           com.ljhj.app                         I  Skipped 123 frames!  The application may be doing too much work on its main thread.
2025-07-07 17:54:42.680  9427-9466  ProfileInstaller        com.ljhj.app                         D  Installing profile for com.ljhj.app
2025-07-07 17:54:42.713  9427-9450  BufferPoolAccessor2.0   com.ljhj.app                         D  bufferpool2 0x7e71612a43f8 : 0(0 size) total buffers - 0(0 size) used buffers - 0/5 (recycle/alloc) - 3/6 (fetch/transfer)
2025-07-07 17:54:42.713  9427-9450  BufferPoolAccessor2.0   com.ljhj.app                         D  evictor expired: 1, evicted: 1
2025-07-07 17:54:43.713  9427-9450  BufferPoolAccessor2.0   com.ljhj.app                         D  bufferpool2 0x7e71612a3618 : 0(0 size) total buffers - 0(0 size) used buffers - 0/5 (recycle/alloc) - 1/2 (fetch/transfer)
2025-07-07 17:54:43.714  9427-9450  BufferPoolAccessor2.0   com.ljhj.app                         D  evictor expired: 1, evicted: 1
2025-07-07 18:40:04.211  9760-9760  com.ljhj.app            com.ljhj.app                         I  Late-enabling -Xcheck:jni
2025-07-07 18:40:04.317  9760-9760  com.ljhj.app            com.ljhj.app                         I  Using CollectorTypeCMC GC.
2025-07-07 18:40:04.319  9760-9760  com.ljhj.app            com.ljhj.app                         W  Unexpected CPU variant for x86: x86_64.
                                                                                                    Known variants: atom, sandybridge, silvermont, goldmont, goldmont-plus, goldmont-without-sha-xsaves, tremont, kabylake, default
2025-07-07 18:40:04.366  9760-9760  nativeloader            com.ljhj.app                         D  Load libframework-connectivity-tiramisu-jni.so using APEX ns com_android_tethering for caller /apex/com.android.tethering/javalib/framework-connectivity-t.jar: ok
2025-07-07 18:40:04.664  9760-9760  com.ljhj.app            com.ljhj.app                         W  Suspending all threads took: 173.865ms
2025-07-07 18:40:04.666  9760-9760  nativeloader            com.ljhj.app                         D  Load /data/user/0/com.ljhj.app/code_cache/startup_agents/9758b833-agent.so using system ns (caller=<unknown>): ok
2025-07-07 18:40:04.661  9760-9760  re-initialized>         com.ljhj.app                         W  type=1400 audit(0.0:849): avc:  granted  { execute } for  path="/data/data/com.ljhj.app/code_cache/startup_agents/9758b833-agent.so" dev="dm-46" ino=362603 scontext=u:r:untrusted_app:s0:c209,c256,c512,c768 tcontext=u:object_r:app_data_file:s0:c209,c256,c512,c768 tclass=file app=com.ljhj.app
2025-07-07 18:40:04.755  9760-9760  com.ljhj.app            com.ljhj.app                         W  DexFile /data/data/com.ljhj.app/code_cache/.studio/instruments-07dd17c6.jar is in boot class path but is not in a known location
2025-07-07 18:40:05.065  9760-9760  com.ljhj.app            com.ljhj.app                         W  Redefining intrinsic method java.lang.Thread java.lang.Thread.currentThread(). This may cause the unexpected use of the original definition of java.lang.Thread java.lang.Thread.currentThread()in methods that have already been compiled.
2025-07-07 18:40:05.065  9760-9760  com.ljhj.app            com.ljhj.app                         W  Redefining intrinsic method boolean java.lang.Thread.interrupted(). This may cause the unexpected use of the original definition of boolean java.lang.Thread.interrupted()in methods that have already been compiled.
2025-07-07 18:40:05.189  9760-9760  ziparchive              com.ljhj.app                         W  Unable to open '/data/data/com.ljhj.app/code_cache/.overlay/base.apk/classes3.dm': No such file or directory
2025-07-07 18:40:05.191  9760-9760  ziparchive              com.ljhj.app                         W  Unable to open '/data/data/com.ljhj.app/code_cache/.overlay/base.apk/classes4.dm': No such file or directory
2025-07-07 18:40:05.192  9760-9760  ziparchive              com.ljhj.app                         W  Unable to open '/data/app/~~gtHxtHjs8sRDYZO0S7SLUg==/com.ljhj.app-UA-zE0ayFnhZBosso2KRHg==/base.dm': No such file or directory
2025-07-07 18:40:05.192  9760-9760  ziparchive              com.ljhj.app                         W  Unable to open '/data/app/~~gtHxtHjs8sRDYZO0S7SLUg==/com.ljhj.app-UA-zE0ayFnhZBosso2KRHg==/base.dm': No such file or directory
2025-07-07 18:40:06.657  9760-9760  nativeloader            com.ljhj.app                         D  Configuring clns-7 for other apk /data/app/~~gtHxtHjs8sRDYZO0S7SLUg==/com.ljhj.app-UA-zE0ayFnhZBosso2KRHg==/base.apk. target_sdk_version=36, uses_libraries=, library_path=/data/app/~~gtHxtHjs8sRDYZO0S7SLUg==/com.ljhj.app-UA-zE0ayFnhZBosso2KRHg==/lib/x86_64:/data/app/~~gtHxtHjs8sRDYZO0S7SLUg==/com.ljhj.app-UA-zE0ayFnhZBosso2KRHg==/base.apk!/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.ljhj.app
2025-07-07 18:40:06.724  9760-9760  GraphicsEnvironment     com.ljhj.app                         V  Currently set values for:
2025-07-07 18:40:06.724  9760-9760  GraphicsEnvironment     com.ljhj.app                         V    angle_gl_driver_selection_pkgs=[]
2025-07-07 18:40:06.724  9760-9760  GraphicsEnvironment     com.ljhj.app                         V    angle_gl_driver_selection_values=[]
2025-07-07 18:40:06.724  9760-9760  GraphicsEnvironment     com.ljhj.app                         V  Global.Settings values are invalid: number of packages: 0, number of values: 0
2025-07-07 18:40:06.724  9760-9760  GraphicsEnvironment     com.ljhj.app                         V  Neither updatable production driver nor prerelease driver is supported.
2025-07-07 18:40:07.047  9760-9782  MediaCodecList          com.ljhj.app                         D  codecHandlesFormat: no format, so no extra checks
2025-07-07 18:40:07.056  9760-9783  CCodec                  com.ljhj.app                         D  allocate(c2.android.raw.decoder)
2025-07-07 18:40:07.065  9760-9783  Codec2Client            com.ljhj.app                         I  Available Codec2 services: "default" "software"
2025-07-07 18:40:07.073  9760-9783  CCodec                  com.ljhj.app                         I  setting up 'default' as default (vendor) store
2025-07-07 18:40:07.082  9760-9783  CCodec                  com.ljhj.app                         I  Created component [c2.android.raw.decoder]
2025-07-07 18:40:07.082  9760-9783  CCodecConfig            com.ljhj.app                         D  read media type: audio/raw
2025-07-07 18:40:07.084  9760-9783  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: algo.buffers.max-count.values
2025-07-07 18:40:07.085  9760-9783  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: output.subscribed-indices.values
2025-07-07 18:40:07.085  9760-9783  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: input.buffers.allocator-ids.values
2025-07-07 18:40:07.085  9760-9783  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: output.buffers.allocator-ids.values
2025-07-07 18:40:07.085  9760-9783  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: algo.buffers.allocator-ids.values
2025-07-07 18:40:07.086  9760-9783  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: output.buffers.pool-ids.values
2025-07-07 18:40:07.086  9760-9783  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: algo.buffers.pool-ids.values
2025-07-07 18:40:07.088  9760-9783  CCodecConfig            com.ljhj.app                         I  query failed after returning 9 values (BAD_INDEX)
2025-07-07 18:40:07.088  9760-9783  CCodecConfig            com.ljhj.app                         D  c2 config diff is Dict {
                                                                                                      c2::u32 coded.bitrate.value = 64000
                                                                                                      c2::u32 input.buffers.max-size.value = 65536
                                                                                                      c2::u32 input.delay.value = 0
                                                                                                      string input.media-type.value = "audio/raw"
                                                                                                      c2::u32 output.large-frame.max-size = 0
                                                                                                      c2::u32 output.large-frame.threshold-size = 0
                                                                                                      string output.media-type.value = "audio/raw"
                                                                                                      c2::u32 raw.channel-count.value = 2
                                                                                                      c2::u32 raw.pcm-encoding.value = 0
                                                                                                      c2::u32 raw.sample-rate.value = 44100
                                                                                                    }
2025-07-07 18:40:07.094  9760-9783  CCodec                  com.ljhj.app                         D  [c2.android.raw.decoder] buffers are bound to CCodec for this session
2025-07-07 18:40:07.095  9760-9783  CCodecConfig            com.ljhj.app                         D  no c2 equivalents for durationUs
2025-07-07 18:40:07.095  9760-9783  CCodecConfig            com.ljhj.app                         D  no c2 equivalents for track-id
2025-07-07 18:40:07.095  9760-9783  CCodecConfig            com.ljhj.app                         D  no c2 equivalents for bits-per-sample
2025-07-07 18:40:07.095  9760-9783  CCodecConfig            com.ljhj.app                         D  no c2 equivalents for flags
2025-07-07 18:40:07.096  9760-9783  Codec2Client            com.ljhj.app                         W  query -- param skipped: index = 1107298332.
2025-07-07 18:40:07.096  9760-9783  CCodec                  com.ljhj.app                         D  encoding statistics level = 0
2025-07-07 18:40:07.096  9760-9783  CCodec                  com.ljhj.app                         D  setup formats input: AMessage(what = 0x00000000) = {
                                                                                                      int32_t android._codec-pcm-encoding = 2
                                                                                                      int32_t bitrate = 64000
                                                                                                      int32_t channel-count = 2
                                                                                                      int32_t max-input-size = 65536
                                                                                                      string mime = "audio/raw"
                                                                                                      int32_t pcm-encoding = 2
                                                                                                      int32_t sample-rate = 44100
                                                                                                    }
2025-07-07 18:40:07.096  9760-9783  CCodec                  com.ljhj.app                         D  setup formats output: AMessage(what = 0x00000000) = {
                                                                                                      int32_t android._codec-pcm-encoding = 2
                                                                                                      int32_t buffer-batch-max-output-size = 0
                                                                                                      int32_t buffer-batch-threshold-output-size = 0
                                                                                                      int32_t channel-count = 2
                                                                                                      string mime = "audio/raw"
                                                                                                      int32_t pcm-encoding = 2
                                                                                                      int32_t sample-rate = 44100
                                                                                                      int32_t channel-mask = 0
                                                                                                      int32_t android._config-pcm-encoding = 2
                                                                                                    }
2025-07-07 18:40:07.097  9760-9783  CCodecConfig            com.ljhj.app                         I  query failed after returning 9 values (BAD_INDEX)
2025-07-07 18:40:07.098  9760-9783  Codec2Client            com.ljhj.app                         W  query -- param skipped: index = 1342179345.
2025-07-07 18:40:07.098  9760-9783  Codec2Client            com.ljhj.app                         W  query -- param skipped: index = 2415921170.
2025-07-07 18:40:07.098  9760-9783  C2Store                 com.ljhj.app                         D  Using DMABUF Heaps
2025-07-07 18:40:07.099  9760-9783  CCodecBufferChannel     com.ljhj.app                         D  [c2.android.raw.decoder#339] Created input block pool with allocatorID 16 => poolID 17 - OK (0)
2025-07-07 18:40:07.100  9760-9783  CCodecBufferChannel     com.ljhj.app                         I  [c2.android.raw.decoder#339] Created output block pool with allocatorID 16 => poolID 82 - OK
2025-07-07 18:40:07.101  9760-9783  CCodecBufferChannel     com.ljhj.app                         D  [c2.android.raw.decoder#339] Configured output block pool ids 82 => OK
2025-07-07 18:40:07.102  9760-9783  DMABUFHEAPS             com.ljhj.app                         I  Using DMA-BUF heap named: system
2025-07-07 18:40:07.122  9760-9783  CCodecBufferChannel     com.ljhj.app                         D  [c2.android.raw.decoder#339] MediaCodec discarded an unknown buffer
2025-07-07 18:40:07.122  9760-9783  CCodecBufferChannel     com.ljhj.app                         D  [c2.android.raw.decoder#339] MediaCodec discarded an unknown buffer
2025-07-07 18:40:07.122  9760-9783  CCodecBufferChannel     com.ljhj.app                         D  [c2.android.raw.decoder#339] MediaCodec discarded an unknown buffer
2025-07-07 18:40:07.126  9760-9783  hw-BpHwBinder           com.ljhj.app                         I  onLastStrongRef automatically unlinking death recipients
2025-07-07 18:40:07.136  9760-9782  MediaCodecList          com.ljhj.app                         D  codecHandlesFormat: no format, so no extra checks
2025-07-07 18:40:07.138  9760-9792  CCodec                  com.ljhj.app                         D  allocate(c2.android.raw.decoder)
2025-07-07 18:40:07.141  9760-9792  CCodec                  com.ljhj.app                         I  setting up 'default' as default (vendor) store
2025-07-07 18:40:07.145  9760-9792  CCodec                  com.ljhj.app                         I  Created component [c2.android.raw.decoder]
2025-07-07 18:40:07.146  9760-9792  CCodecConfig            com.ljhj.app                         D  read media type: audio/raw
2025-07-07 18:40:07.153  9760-9792  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: algo.buffers.max-count.values
2025-07-07 18:40:07.154  9760-9792  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: output.subscribed-indices.values
2025-07-07 18:40:07.154  9760-9792  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: input.buffers.allocator-ids.values
2025-07-07 18:40:07.154  9760-9792  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: output.buffers.allocator-ids.values
2025-07-07 18:40:07.155  9760-9792  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: algo.buffers.allocator-ids.values
2025-07-07 18:40:07.156  9760-9792  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: output.buffers.pool-ids.values
2025-07-07 18:40:07.156  9760-9792  ReflectedParamUpdater   com.ljhj.app                         D  extent() != 1 for single value type: algo.buffers.pool-ids.values
2025-07-07 18:40:07.158  9760-9792  CCodecConfig            com.ljhj.app                         I  query failed after returning 9 values (BAD_INDEX)
2025-07-07 18:40:07.158  9760-9792  CCodecConfig            com.ljhj.app                         D  c2 config diff is Dict {
                                                                                                      c2::u32 coded.bitrate.value = 64000
                                                                                                      c2::u32 input.buffers.max-size.value = 65536
                                                                                                      c2::u32 input.delay.value = 0
                                                                                                      string input.media-type.value = "audio/raw"
                                                                                                      c2::u32 output.large-frame.max-size = 0
                                                                                                      c2::u32 output.large-frame.threshold-size = 0
                                                                                                      string output.media-type.value = "audio/raw"
                                                                                                      c2::u32 raw.channel-count.value = 2
                                                                                                      c2::u32 raw.pcm-encoding.value = 0
                                                                                                      c2::u32 raw.sample-rate.value = 44100
                                                                                                    }
2025-07-07 18:40:07.162  9760-9792  CCodec                  com.ljhj.app                         D  [c2.android.raw.decoder] buffers are bound to CCodec for this session
2025-07-07 18:40:07.162  9760-9792  CCodecConfig            com.ljhj.app                         D  no c2 equivalents for durationUs
2025-07-07 18:40:07.162  9760-9792  CCodecConfig            com.ljhj.app                         D  no c2 equivalents for track-id
2025-07-07 18:40:07.162  9760-9792  CCodecConfig            com.ljhj.app                         D  no c2 equivalents for bits-per-sample
2025-07-07 18:40:07.162  9760-9792  CCodecConfig            com.ljhj.app                         D  no c2 equivalents for flags
2025-07-07 18:40:07.163  9760-9792  CCodecConfig            com.ljhj.app                         D  c2 config diff is   c2::u32 raw.sample-rate.value = 11025
2025-07-07 18:40:07.163  9760-9792  Codec2Client            com.ljhj.app                         W  query -- param skipped: index = 1107298332.
2025-07-07 18:40:07.163  9760-9792  CCodec                  com.ljhj.app                         D  encoding statistics level = 0
2025-07-07 18:40:07.163  9760-9792  CCodec                  com.ljhj.app                         D  setup formats input: AMessage(what = 0x00000000) = {
                                                                                                      int32_t android._codec-pcm-encoding = 2
                                                                                                      int32_t bitrate = 64000
                                                                                                      int32_t channel-count = 2
                                                                                                      int32_t max-input-size = 65536
                                                                                                      string mime = "audio/raw"
                                                                                                      int32_t pcm-encoding = 2
                                                                                                      int32_t sample-rate = 11025
                                                                                                    }
2025-07-07 18:40:07.164  9760-9792  CCodec                  com.ljhj.app                         D  setup formats output: AMessage(what = 0x00000000) = {
                                                                                                      int32_t android._codec-pcm-encoding = 2
                                                                                                      int32_t buffer-batch-max-output-size = 0
                                                                                                      int32_t buffer-batch-threshold-output-size = 0
                                                                                                      int32_t channel-count = 2
                                                                                                      string mime = "audio/raw"
                                                                                                      int32_t pcm-encoding = 2
                                                                                                      int32_t sample-rate = 11025
                                                                                                      int32_t channel-mask = 0
                                                                                                      int32_t android._config-pcm-encoding = 2
                                                                                                    }
2025-07-07 18:40:07.164  9760-9792  CCodecConfig            com.ljhj.app                         I  query failed after returning 9 values (BAD_INDEX)
2025-07-07 18:40:07.165  9760-9792  Codec2Client            com.ljhj.app                         W  query -- param skipped: index = 1342179345.
2025-07-07 18:40:07.165  9760-9792  Codec2Client            com.ljhj.app                         W  query -- param skipped: index = 2415921170.
2025-07-07 18:40:07.166  9760-9792  CCodecBufferChannel     com.ljhj.app                         D  [c2.android.raw.decoder#339] Created input block pool with allocatorID 16 => poolID 18 - OK (0)
2025-07-07 18:40:07.166  9760-9792  CCodecBufferChannel     com.ljhj.app                         I  [c2.android.raw.decoder#339] Created output block pool with allocatorID 16 => poolID 84 - OK
2025-07-07 18:40:07.167  9760-9792  CCodecBufferChannel     com.ljhj.app                         D  [c2.android.raw.decoder#339] Configured output block pool ids 84 => OK
2025-07-07 18:40:07.206  9760-9792  CCodecBufferChannel     com.ljhj.app                         D  [c2.android.raw.decoder#339] MediaCodec discarded an unknown buffer
2025-07-07 18:40:07.206  9760-9792  CCodecBufferChannel     com.ljhj.app                         D  [c2.android.raw.decoder#339] MediaCodec discarded an unknown buffer
2025-07-07 18:40:07.209  9760-9792  hw-BpHwBinder           com.ljhj.app                         I  onLastStrongRef automatically unlinking death recipients
2025-07-07 18:40:07.222  9760-9760  HWUI                    com.ljhj.app                         W  Unknown dataspace 0
2025-07-07 18:40:07.662  9760-9764  com.ljhj.app            com.ljhj.app                         I  Compiler allocated 5174KB to compile void android.view.ViewRootImpl.performTraversals()
2025-07-07 18:40:08.790  9760-9778  EGL_emulation           com.ljhj.app                         I  Opening libGLESv1_CM_emulation.so
2025-07-07 18:40:08.794  9760-9778  EGL_emulation           com.ljhj.app                         I  Opening libGLESv2_emulation.so
2025-07-07 18:40:08.831  9760-9778  HWUI                    com.ljhj.app                         W  Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
2025-07-07 18:40:08.831  9760-9778  HWUI                    com.ljhj.app                         W  Failed to initialize 101010-2 format, error = EGL_SUCCESS
2025-07-07 18:40:08.930  9760-9778  Gralloc4                com.ljhj.app                         I  mapper 4.x is not supported
2025-07-07 18:40:09.143  9760-9795  HWUI                    com.ljhj.app                         I  Davey! duration=1882ms; Flags=1, FrameTimelineVsyncId=250494, IntendedVsync=7321468334254, Vsync=7321468334254, InputEventId=0, HandleInputStart=7321481659020, AnimationStart=7321481713020, PerformTraversalsStart=7321481768220, DrawStart=7323206415020, FrameDeadline=7321485000920, FrameInterval=7321481622620, FrameStartTime=16666666, SyncQueued=7323256618920, SyncStart=7323258045120, IssueDrawCommandsStart=7323262345520, SwapBuffers=7323331089220, FrameCompleted=7323352404720, DequeueBufferDuration=36200, QueueBufferDuration=719400, GpuCompleted=7323352404720, SwapBuffersCompleted=7323335780920, DisplayPresentTime=72904454231359566, CommandSubmissionCompleted=7323331089220, 
2025-07-07 18:40:09.149  9760-9760  Choreographer           com.ljhj.app                         I  Skipped 98 frames!  The application may be doing too much work on its main thread.
2025-07-07 18:40:11.919  9760-9803  ProfileInstaller        com.ljhj.app                         D  Installing profile for com.ljhj.app
2025-07-07 18:40:12.104  9760-9786  BufferPoolAccessor2.0   com.ljhj.app                         D  bufferpool2 0x7e71612a4d38 : 0(0 size) total buffers - 0(0 size) used buffers - 0/5 (recycle/alloc) - 3/6 (fetch/transfer)
2025-07-07 18:40:12.104  9760-9786  BufferPoolAccessor2.0   com.ljhj.app                         D  evictor expired: 1, evicted: 1
2025-07-07 18:40:13.104  9760-9786  BufferPoolAccessor2.0   com.ljhj.app                         D  bufferpool2 0x7e71612a43f8 : 0(0 size) total buffers - 0(0 size) used buffers - 0/5 (recycle/alloc) - 1/2 (fetch/transfer)
2025-07-07 18:40:13.104  9760-9786  BufferPoolAccessor2.0   com.ljhj.app                         D  evictor expired: 1, evicted: 1
2025-07-07 18:40:14.116  9760-9760  InsetsController        com.ljhj.app                         D  show(ime(), fromIme=false)
2025-07-07 18:40:14.117  9760-9760  ImeTracker              com.ljhj.app                         I  com.ljhj.app:d9a56734: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
2025-07-07 18:40:14.120  9760-9760  InputMethodManager      com.ljhj.app                         D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{822a058 VFED..... .F....ID 0,0-1080,2424 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT_BY_INSETS_API
2025-07-07 18:40:14.133  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=1256.19ms min=4.17ms max=4545.90ms count=4
2025-07-07 18:40:14.150  9760-9760  ImeTracker              com.ljhj.app                         I  com.ljhj.app:26580b80: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
2025-07-07 18:40:14.151  9760-9760  InputMethodManager      com.ljhj.app                         D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{822a058 VFED..... .F...... 0,0-1080,2424 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
2025-07-07 18:40:14.428  9760-9765  com.ljhj.app            com.ljhj.app                         I  Background concurrent mark compact GC freed 3873KB AllocSpace bytes, 0(0B) LOS objects, 49% free, 4844KB/9689KB, paused 1.078ms,9.509ms total 96.895ms
2025-07-07 18:40:14.629  9760-9760  InsetsController        com.ljhj.app                         D  show(ime(), fromIme=true)
2025-07-07 18:40:14.636  9760-9760  InsetsController        com.ljhj.app                         D  show(ime(), fromIme=true)
2025-07-07 18:40:14.636  9760-9760  ImeTracker              com.ljhj.app                         I  com.ljhj.app:26580b80: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
2025-07-07 18:40:14.642  9760-9810  InteractionJankMonitor  com.ljhj.app                         W  Initializing without READ_DEVICE_CONFIG permission. enabled=false, interval=1, missedFrameThreshold=3, frameTimeThreshold=64, package=com.ljhj.app
2025-07-07 18:40:14.857  9760-9760  ImeTracker              com.ljhj.app                         I  com.ljhj.app:d9a56734: onShown
2025-07-07 18:40:15.158  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=112.13ms min=16.48ms max=493.35ms count=9
2025-07-07 18:40:16.654  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=498.46ms min=495.01ms max=501.01ms count=3
2025-07-07 18:40:17.672  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=508.43ms min=498.51ms max=518.36ms count=2
2025-07-07 18:40:19.172  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=499.82ms min=498.74ms max=501.24ms count=3
2025-07-07 18:40:20.671  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=499.30ms min=498.77ms max=499.61ms count=3
2025-07-07 18:40:21.671  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=499.87ms min=499.34ms max=500.40ms count=2
2025-07-07 18:40:22.686  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=507.47ms min=500.34ms max=514.60ms count=2
2025-07-07 18:40:23.729  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=521.26ms min=511.86ms max=530.65ms count=2
2025-07-07 18:40:24.740  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=504.87ms min=423.55ms max=586.19ms count=2
2025-07-07 18:40:25.753  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=506.01ms min=499.45ms max=512.57ms count=2
2025-07-07 18:40:26.757  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=501.05ms min=492.20ms max=509.90ms count=2
2025-07-07 18:40:27.787  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=514.53ms min=495.76ms max=533.29ms count=2
2025-07-07 18:40:28.790  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=501.16ms min=499.88ms max=502.44ms count=2
2025-07-07 18:40:29.804  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=506.49ms min=499.27ms max=513.71ms count=2
2025-07-07 18:40:30.520  9760-9760  InsetsController        com.ljhj.app                         D  show(ime(), fromIme=false)
2025-07-07 18:40:30.521  9760-9760  ImeTracker              com.ljhj.app                         I  com.ljhj.app:df3419ec: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
2025-07-07 18:40:30.521  9760-9760  ImeTracker              com.ljhj.app                         I  com.ljhj.app:df3419ec: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
2025-07-07 18:40:30.534  9760-9760  ImeTracker              com.ljhj.app                         I  com.ljhj.app:539e2af5: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
2025-07-07 18:40:30.535  9760-9760  InputMethodManager      com.ljhj.app                         D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{822a058 VFED..... .F...... 0,0-1080,2424 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
2025-07-07 18:40:30.541  9760-9760  InsetsController        com.ljhj.app                         D  show(ime(), fromIme=true)
2025-07-07 18:40:30.542  9760-9760  ImeTracker              com.ljhj.app                         I  com.ljhj.app:539e2af5: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
2025-07-07 18:40:31.306  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=375.07ms min=230.24ms max=502.28ms count=4
2025-07-07 18:40:32.824  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=505.93ms min=497.86ms max=519.96ms count=3
2025-07-07 18:40:34.322  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=498.80ms min=494.90ms max=502.01ms count=3
2025-07-07 18:40:35.323  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=499.81ms min=496.96ms max=502.65ms count=2
2025-07-07 18:40:36.325  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=500.50ms min=500.20ms max=500.81ms count=2
2025-07-07 18:40:37.842  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=505.18ms min=496.19ms max=519.15ms count=3
2025-07-07 18:40:38.187  9760-9760  InsetsController        com.ljhj.app                         D  show(ime(), fromIme=false)
2025-07-07 18:40:38.189  9760-9760  ImeTracker              com.ljhj.app                         I  com.ljhj.app:e490e7c: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
2025-07-07 18:40:38.190  9760-9760  ImeTracker              com.ljhj.app                         I  com.ljhj.app:e490e7c: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
2025-07-07 18:40:38.205  9760-9760  ImeTracker              com.ljhj.app                         I  com.ljhj.app:699b0aef: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
2025-07-07 18:40:38.206  9760-9760  InputMethodManager      com.ljhj.app                         D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{822a058 VFED..... .F...... 0,0-1080,2424 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
2025-07-07 18:40:38.212  9760-9760  InsetsController        com.ljhj.app                         D  show(ime(), fromIme=true)
2025-07-07 18:40:38.212  9760-9760  ImeTracker              com.ljhj.app                         I  com.ljhj.app:699b0aef: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
2025-07-07 18:40:39.338  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=373.85ms min=133.88ms max=499.62ms count=4
2025-07-07 18:40:40.837  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=498.46ms min=496.53ms max=500.01ms count=3
2025-07-07 18:40:41.838  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=500.32ms min=498.09ms max=502.55ms count=2
2025-07-07 18:40:42.859  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=510.47ms min=501.37ms max=519.57ms count=2
2025-07-07 18:40:43.888  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=165.08ms min=6.12ms max=497.87ms count=6
2025-07-07 18:40:44.954  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=174.86ms min=4.14ms max=499.11ms count=6
2025-07-07 18:40:45.972  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=253.92ms min=15.39ms max=516.37ms count=4
2025-07-07 18:40:46.978  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=502.67ms min=501.13ms max=504.20ms count=2
2025-07-07 18:40:47.525  9760-9760  InsetsController        com.ljhj.app                         D  show(ime(), fromIme=false)
2025-07-07 18:40:47.525  9760-9760  ImeTracker              com.ljhj.app                         I  com.ljhj.app:d8885929: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
2025-07-07 18:40:47.525  9760-9760  ImeTracker              com.ljhj.app                         I  com.ljhj.app:d8885929: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
2025-07-07 18:40:47.545  9760-9760  ImeTracker              com.ljhj.app                         I  com.ljhj.app:fc73952c: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
2025-07-07 18:40:47.545  9760-9760  InputMethodManager      com.ljhj.app                         D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{822a058 VFED..... .F...... 0,0-1080,2424 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
2025-07-07 18:40:47.715  9760-9760  InsetsController        com.ljhj.app                         D  show(ime(), fromIme=true)
2025-07-07 18:40:47.717  9760-9760  ImeTracker              com.ljhj.app                         I  com.ljhj.app:837c148f: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
2025-07-07 18:40:47.717  9760-9760  InsetsController        com.ljhj.app                         D  show(ime(), fromIme=true)
2025-07-07 18:40:47.718  9760-9760  ImeTracker              com.ljhj.app                         I  com.ljhj.app:fc73952c: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
2025-07-07 18:40:48.056  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=84.25ms min=4.07ms max=495.61ms count=12
2025-07-07 18:40:49.555  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=499.28ms min=497.16ms max=500.92ms count=3
2025-07-07 18:40:50.635  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=359.94ms min=80.55ms max=499.67ms count=3
2025-07-07 18:40:51.664  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=97.52ms min=5.90ms max=202.61ms count=10
2025-07-07 18:40:52.689  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=340.22ms min=10.11ms max=517.21ms count=3
2025-07-07 18:40:54.187  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=499.20ms min=497.32ms max=501.16ms count=3
2025-07-07 18:40:55.190  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=500.91ms min=496.83ms max=504.99ms count=2
2025-07-07 18:40:55.431  9760-9818  TrafficStats            com.ljhj.app                         D  tagSocket(120) with statsTag=0xffffffff, statsUid=-1
2025-07-07 18:40:55.629  9760-9818  LoginResponse           com.ljhj.app                         D  {"code":1,"msg":"登录成功","time":"1751884863","data":{"userinfo":{"id":1,"username":"admin","nickname":"admin","mobile":"13000000000","avatar":"\/assets\/img\/avatar.png","score":0,"token":"4f81b1aa-f015-4251-839d-0657c3632762","user_id":1,"createtime":1751884863,"expiretime":1754476863,"expires_in":2592000}}}
2025-07-07 18:40:56.691  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=15.98ms min=3.00ms max=505.43ms count=55
2025-07-07 18:40:57.708  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=507.99ms min=504.45ms max=511.53ms count=2
2025-07-07 18:40:59.207  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=499.16ms min=498.06ms max=500.35ms count=3
2025-07-07 18:41:00.209  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=500.85ms min=499.77ms max=501.92ms count=2
2025-07-07 18:41:01.706  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=498.93ms min=494.92ms max=501.18ms count=3
2025-07-07 18:41:02.723  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=508.14ms min=502.74ms max=513.54ms count=2
2025-07-07 18:41:03.724  9760-9778  EGL_emulation           com.ljhj.app                         D  app_time_stats: avg=500.09ms min=496.74ms max=503.44ms count=2
