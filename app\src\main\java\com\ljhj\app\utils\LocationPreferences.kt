package com.ljhj.app.utils

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.first

/**
 * 位置服务偏好设置管理
 */
object LocationPreferences {
    
    private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "location_preferences")
    
    private val USE_GOOGLE_SERVICES_KEY = booleanPreferencesKey("use_google_services")
    private val FORCE_SYSTEM_LOCATION_KEY = booleanPreferencesKey("force_system_location")
    
    /**
     * 检查是否为国产手机
     * 包含主要的国产手机品牌，这些设备通常缺少或修改了Google Play Services
     */
    private fun isChineseManufacturer(): Boolean {
        val manufacturer = android.os.Build.MANUFACTURER.lowercase()
        return manufacturer.contains("huawei") ||
               manufacturer.contains("xiaomi") ||
               manufacturer.contains("oppo") ||
               manufacturer.contains("vivo") ||
               manufacturer.contains("oneplus") ||
               manufacturer.contains("realme") ||
               manufacturer.contains("honor") ||
               manufacturer.contains("meizu") ||
               manufacturer.contains("redmi") ||
               manufacturer.contains("poco") ||
               manufacturer.contains("iqoo") ||
               manufacturer.contains("nubia") ||
               manufacturer.contains("zte") ||
               manufacturer.contains("tcl") ||
               manufacturer.contains("coolpad") ||
               manufacturer.contains("gionee") ||
               manufacturer.contains("lenovo") ||
               manufacturer.contains("motorola") // 联想旗下
    }

    /**
     * 获取是否使用Google Play Services的设置
     * 针对国产手机优化：默认不使用Google服务
     */
    fun getUseGoogleServices(context: Context): Flow<Boolean> {
        return context.dataStore.data.map { preferences ->
            // 默认值：国产手机默认不使用Google服务，非国产手机也默认不使用（系统GPS更稳定）
            preferences[USE_GOOGLE_SERVICES_KEY] ?: false
        }
    }
    
    /**
     * 设置是否使用Google Play Services
     */
    suspend fun setUseGoogleServices(context: Context, useGoogleServices: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[USE_GOOGLE_SERVICES_KEY] = useGoogleServices
        }
        Logger.location("Location preference updated: useGoogleServices = $useGoogleServices")
    }
    
    /**
     * 获取是否强制使用系统定位的设置
     */
    fun getForceSystemLocation(context: Context): Flow<Boolean> {
        return context.dataStore.data.map { preferences ->
            preferences[FORCE_SYSTEM_LOCATION_KEY] ?: false
        }
    }
    
    /**
     * 设置是否强制使用系统定位
     */
    suspend fun setForceSystemLocation(context: Context, forceSystemLocation: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[FORCE_SYSTEM_LOCATION_KEY] = forceSystemLocation
        }
        Logger.location("Location preference updated: forceSystemLocation = $forceSystemLocation")
    }
    
    /**
     * 重置所有位置偏好设置
     */
    suspend fun resetLocationPreferences(context: Context) {
        context.dataStore.edit { preferences ->
            preferences.remove(USE_GOOGLE_SERVICES_KEY)
            preferences.remove(FORCE_SYSTEM_LOCATION_KEY)
        }
        Logger.location("Location preferences reset")
    }
    
    /**
     * 获取推荐的位置服务提供商
     * 针对国产手机优化：默认优先使用系统GPS，Google Play Services作为备用
     */
    suspend fun getRecommendedLocationProvider(context: Context): LocationProvider {
        val forceSystem = getForceSystemLocation(context).first()
        if (forceSystem) {
            Logger.location("User forced system location only")
            return LocationProvider.SYSTEM_ONLY
        }

        val useGoogle = getUseGoogleServices(context).first()

        // 针对国产手机的优化策略
        if (isChineseManufacturer()) {
            Logger.location("Chinese manufacturer detected: ${android.os.Build.MANUFACTURER}")
            // 国产手机默认使用系统定位优先，除非用户明确要求使用Google服务
            return if (useGoogle) {
                Logger.location("User explicitly enabled Google services for Chinese device")
                LocationProvider.GOOGLE_FIRST
            } else {
                Logger.location("Using system location first for Chinese device")
                LocationProvider.SYSTEM_FIRST
            }
        }

        // 对于非国产手机，也默认使用系统定位优先（更稳定可靠）
        return if (useGoogle) {
            Logger.location("Using Google services first for non-Chinese device")
            LocationProvider.GOOGLE_FIRST
        } else {
            Logger.location("Using system location first for non-Chinese device")
            LocationProvider.SYSTEM_FIRST
        }
    }
    
    /**
     * 位置服务提供商枚举
     */
    enum class LocationProvider {
        GOOGLE_FIRST,    // 优先使用Google Play Services
        SYSTEM_FIRST,    // 优先使用系统定位
        SYSTEM_ONLY      // 仅使用系统定位
    }
}
