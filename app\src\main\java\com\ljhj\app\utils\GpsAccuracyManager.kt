package com.ljhj.app.utils

import android.location.Location
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext

/**
 * GPS精度管理器
 */
object GpsAccuracyManager {
    
    // 精度等级定义
    const val EXCELLENT_ACCURACY = 10f      // 优秀精度
    const val GOOD_ACCURACY = 50f           // 良好精度
    const val ACCEPTABLE_ACCURACY = 100f    // 可接受精度
    const val POOR_ACCURACY = 300f          // 较差精度
    
    /**
     * 获取精度等级描述
     */
    fun getAccuracyDescription(accuracy: Float): String {
        return when {
            accuracy <= EXCELLENT_ACCURACY -> "优秀"
            accuracy <= GOOD_ACCURACY -> "良好"
            accuracy <= ACCEPTABLE_ACCURACY -> "可接受"
            accuracy <= POOR_ACCURACY -> "较差"
            else -> "很差"
        }
    }
    
    /**
     * 获取精度建议
     */
    fun getAccuracySuggestion(accuracy: Float): String {
        return when {
            accuracy <= GOOD_ACCURACY -> "GPS信号良好，可以正常拍摄"
            accuracy <= ACCEPTABLE_ACCURACY -> "GPS信号一般，建议移动到空旷地带"
            accuracy <= POOR_ACCURACY -> "GPS信号较差，建议移动到室外空旷地带"
            else -> "GPS信号很差，请移动到室外空旷地带或稍后重试"
        }
    }
    
    /**
     * 判断精度是否可接受
     */
    fun isAccuracyAcceptable(accuracy: Float, strictMode: Boolean = false): Boolean {
        return if (strictMode) {
            accuracy <= GOOD_ACCURACY
        } else {
            accuracy <= POOR_ACCURACY
        }
    }
    
    /**
     * 获取推荐的最大等待时间（毫秒）
     */
    fun getRecommendedWaitTime(accuracy: Float): Long {
        return when {
            accuracy <= GOOD_ACCURACY -> 0L
            accuracy <= ACCEPTABLE_ACCURACY -> 10000L  // 10秒
            accuracy <= POOR_ACCURACY -> 20000L        // 20秒
            else -> 30000L                             // 30秒
        }
    }
}

/**
 * GPS精度选择对话框
 */
@Composable
fun GpsAccuracyDialog(
    currentAccuracy: Float,
    onAccept: () -> Unit,
    onWaitForBetter: () -> Unit,
    onCancel: () -> Unit,
    showDialog: Boolean
) {
    if (showDialog) {
        val accuracyDescription = GpsAccuracyManager.getAccuracyDescription(currentAccuracy)
        val suggestion = GpsAccuracyManager.getAccuracySuggestion(currentAccuracy)
        
        AlertDialog(
            onDismissRequest = onCancel,
            title = { Text("GPS精度确认") },
            text = {
                Text(
                    "当前GPS精度: ${currentAccuracy.toInt()}米 ($accuracyDescription)\n\n" +
                    "$suggestion\n\n" +
                    "您可以选择："
                )
            },
            confirmButton = {
                Button(onClick = onAccept) {
                    Text("使用当前精度")
                }
            },
            dismissButton = {
                Button(onClick = onWaitForBetter) {
                    Text("等待更好信号")
                }
            }
        )
    }
}

/**
 * GPS状态管理器
 */
class GpsStatusManager {
    private var _currentLocation by mutableStateOf<Location?>(null)
    private var _isSearching by mutableStateOf(false)
    private var _lastUpdateTime by mutableStateOf(0L)
    private var _searchStartTime by mutableStateOf(0L)
    
    val currentLocation: Location? get() = _currentLocation
    val isSearching: Boolean get() = _isSearching
    val currentAccuracy: Float? get() = _currentLocation?.accuracy
    val searchDuration: Long get() = if (_isSearching) System.currentTimeMillis() - _searchStartTime else 0L
    
    /**
     * 开始搜索GPS
     */
    fun startSearching() {
        Logger.location("Starting GPS search")
        _isSearching = true
        _searchStartTime = System.currentTimeMillis()
    }
    
    /**
     * 停止搜索GPS
     */
    fun stopSearching() {
        Logger.location("Stopping GPS search")
        _isSearching = false
    }
    
    /**
     * 更新位置信息
     */
    fun updateLocation(location: Location) {
        Logger.location("GPS location updated: ${location.latitude}, ${location.longitude}, accuracy: ${location.accuracy}")
        _currentLocation = location
        _lastUpdateTime = System.currentTimeMillis()
        
        // 如果精度足够好，停止搜索
        if (GpsAccuracyManager.isAccuracyAcceptable(location.accuracy)) {
            stopSearching()
        }
    }
    
    /**
     * 清除位置信息
     */
    fun clearLocation() {
        Logger.location("Clearing GPS location")
        _currentLocation = null
        _lastUpdateTime = 0L
    }
    
    /**
     * 检查位置是否过期
     */
    fun isLocationStale(maxAgeMs: Long = 60000L): Boolean {
        return _currentLocation != null && 
               (System.currentTimeMillis() - _lastUpdateTime) > maxAgeMs
    }
    
    /**
     * 获取位置状态描述
     */
    fun getStatusDescription(): String {
        return when {
            _isSearching -> "正在定位中..."
            _currentLocation == null -> "未获取到位置"
            isLocationStale() -> "位置信息已过期"
            else -> {
                val accuracy = _currentLocation?.accuracy ?: 0f
                "位置精度: ${accuracy.toInt()}米 (${GpsAccuracyManager.getAccuracyDescription(accuracy)})"
            }
        }
    }
    
    /**
     * 是否应该显示精度确认对话框
     */
    fun shouldShowAccuracyDialog(): Boolean {
        val location = _currentLocation ?: return false
        return location.accuracy > GpsAccuracyManager.ACCEPTABLE_ACCURACY && 
               location.accuracy <= GpsAccuracyManager.POOR_ACCURACY
    }
    
    /**
     * 重置状态
     */
    fun reset() {
        Logger.location("Resetting GPS status manager")
        _currentLocation = null
        _isSearching = false
        _lastUpdateTime = 0L
        _searchStartTime = 0L
    }
}
