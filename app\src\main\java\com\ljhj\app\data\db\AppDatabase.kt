package com.ljhj.app.data.db

import android.content.Context
import androidx.room.*
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.ljhj.app.data.model.UploadTask

@Database(
    entities = [UploadTask::class],
    version = 2,
    exportSchema = true
)
@TypeConverters(Converters::class)
abstract class AppDatabase : RoomDatabase() {

    abstract fun uploadTaskDao(): UploadTaskDao

    companion object {
        @Volatile
        private var INSTANCE: AppDatabase? = null

        fun getDatabase(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    "ljhj_database"
                )
                .addMigrations(MIGRATION_1_2)
                .build()
                INSTANCE = instance
                instance
            }
        }

        private val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 添加新字段
                database.execSQL("ALTER TABLE upload_tasks ADD COLUMN uploadDate TEXT NOT NULL DEFAULT ''")
                database.execSQL("ALTER TABLE upload_tasks ADD COLUMN serverUrl TEXT")

                // 更新现有记录的uploadDate
                database.execSQL("""
                    UPDATE upload_tasks
                    SET uploadDate = date(timestamp/1000, 'unixepoch', 'localtime')
                    WHERE uploadDate = ''
                """)
            }
        }
    }
}
