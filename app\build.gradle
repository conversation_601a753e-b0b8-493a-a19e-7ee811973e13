plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
    id("kotlin-kapt")
}

android {
    namespace = 'com.ljhj.app'
    compileSdk = 36

    defaultConfig {
        applicationId "com.ljhj.app"
        minSdk 24
        targetSdk 36
        versionCode 2
        versionName "2.1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = '11'
    }
    buildFeatures {
        compose true
    }

    // Room schema export configuration
    kapt {
        arguments {
            arg("room.schemaLocation", "$projectDir/schemas")
        }
    }

    signingConfigs {
        create("release") {
            storeFile = file("my-release-key.jks")
            storePassword = "13793930300"
            keyAlias = "my-key-alias" // TODO: 这里填写你 jks 文件中的实际别名
            keyPassword = "13793930300"
        }
    }
}

dependencies {

    implementation libs.androidx.core.ktx
    implementation libs.androidx.lifecycle.runtime.ktx
    implementation libs.androidx.activity.compose
    implementation platform(libs.androidx.compose.bom)
    implementation libs.androidx.ui
    implementation libs.androidx.ui.graphics
    implementation libs.androidx.ui.tooling.preview
    implementation libs.androidx.material3
    implementation libs.coil.compose
    implementation "androidx.compose.material:material-icons-extended"

    // CameraX dependencies
    implementation "androidx.camera:camera-core:1.3.3"
    implementation "androidx.camera:camera-camera2:1.3.3"
    implementation "androidx.camera:camera-lifecycle:1.3.3"
    implementation "androidx.camera:camera-video:1.3.3"
    implementation "androidx.camera:camera-view:1.3.3"
    implementation "androidx.camera:camera-extensions:1.3.3"
    implementation libs.play.services.location
    implementation "androidx.datastore:datastore-preferences:1.0.0"
    implementation "androidx.navigation:navigation-compose:2.7.7"
    implementation "androidx.compose.foundation:foundation:1.6.8"
    implementation "androidx.compose.foundation:foundation-layout:1.6.8"
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0")
    implementation libs.kotlinx.coroutines.android
    implementation "androidx.compose.runtime:runtime-livedata:1.6.8"

    // WorkManager
    implementation "androidx.work:work-runtime-ktx:2.9.0"

    // Room
    implementation "androidx.room:room-runtime:2.6.1"
    kapt "androidx.room:room-compiler:2.6.1"
    implementation "androidx.room:room-ktx:2.6.1"

    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit
    androidTestImplementation libs.androidx.espresso.core
    androidTestImplementation platform(libs.androidx.compose.bom)
    androidTestImplementation libs.androidx.ui.test.junit4
    debugImplementation libs.androidx.ui.tooling
    debugImplementation libs.androidx.ui.test.manifest
}