package com.ljhj.app.utils

import android.util.Log
import java.text.SimpleDateFormat
import java.util.*

/**
 * 统一日志管理工具
 */
object Logger {
    private const val TAG = "LJHJ_APP"
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
    
    fun d(message: String, tag: String = TAG) {
        Log.d(tag, formatMessage(message))
    }
    
    fun i(message: String, tag: String = TAG) {
        Log.i(tag, formatMessage(message))
    }
    
    fun w(message: String, throwable: Throwable? = null, tag: String = TAG) {
        Log.w(tag, formatMessage(message), throwable)
    }
    
    fun e(message: String, throwable: Throwable? = null, tag: String = TAG) {
        Log.e(tag, formatMessage(message), throwable)
    }
    
    fun network(message: String, url: String? = null) {
        val fullMessage = if (url != null) "[$url] $message" else message
        Log.d("${TAG}_NETWORK", formatMessage(fullMessage))
    }
    
    fun location(message: String) {
        Log.d("${TAG}_LOCATION", formatMessage(message))
    }
    
    fun camera(message: String) {
        Log.d("${TAG}_CAMERA", formatMessage(message))
    }
    
    fun upload(message: String) {
        Log.d("${TAG}_UPLOAD", formatMessage(message))
    }
    
    private fun formatMessage(message: String): String {
        return "[${dateFormat.format(Date())}] $message"
    }
}
