package com.ljhj.app.utils

import android.util.Log
import com.ljhj.app.BuildConfig
import java.text.SimpleDateFormat
import java.util.*

/**
 * 统一日志管理工具
 * 在生产环境中自动禁用调试日志，只保留错误和警告日志
 */
object Logger {
    private const val TAG = "LJHJ_APP"
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())

    /**
     * 调试日志 - 仅在DEBUG模式下输出
     */
    fun d(message: String, tag: String = TAG) {
        if (BuildConfig.DEBUG) {
            Log.d(tag, formatMessage(message))
        }
    }

    /**
     * 信息日志 - 仅在DEBUG模式下输出
     */
    fun i(message: String, tag: String = TAG) {
        if (BuildConfig.DEBUG) {
            Log.i(tag, formatMessage(message))
        }
    }

    /**
     * 警告日志 - 在所有环境下输出（生产环境需要警告信息）
     */
    fun w(message: String, throwable: Throwable? = null, tag: String = TAG) {
        Log.w(tag, formatMessage(message), throwable)
    }

    /**
     * 错误日志 - 在所有环境下输出（生产环境需要错误信息）
     */
    fun e(message: String, throwable: Throwable? = null, tag: String = TAG) {
        Log.e(tag, formatMessage(message), throwable)
    }

    /**
     * 网络日志 - 仅在DEBUG模式下输出
     */
    fun network(message: String, url: String? = null) {
        if (BuildConfig.DEBUG) {
            val fullMessage = if (url != null) "[$url] $message" else message
            Log.d("${TAG}_NETWORK", formatMessage(fullMessage))
        }
    }

    /**
     * 位置日志 - 仅在DEBUG模式下输出
     */
    fun location(message: String) {
        if (BuildConfig.DEBUG) {
            Log.d("${TAG}_LOCATION", formatMessage(message))
        }
    }

    /**
     * 相机日志 - 仅在DEBUG模式下输出
     */
    fun camera(message: String) {
        if (BuildConfig.DEBUG) {
            Log.d("${TAG}_CAMERA", formatMessage(message))
        }
    }

    /**
     * 上传日志 - 仅在DEBUG模式下输出
     */
    fun upload(message: String) {
        if (BuildConfig.DEBUG) {
            Log.d("${TAG}_UPLOAD", formatMessage(message))
        }
    }

    private fun formatMessage(message: String): String {
        return "[${dateFormat.format(Date())}] $message"
    }
}
