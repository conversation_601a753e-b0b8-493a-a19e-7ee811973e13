package com.ljhj.app.network

import android.content.Context
import com.ljhj.app.config.AppConfig
import com.ljhj.app.utils.Logger
import okhttp3.*
import okhttp3.logging.HttpLoggingInterceptor
import java.io.File
import java.io.IOException
import java.util.concurrent.TimeUnit

/**
 * 统一的网络管理器
 */
object NetworkManager {
    
    /**
     * API配置
     */
    object ApiConfig {
        private const val BASE_URL_PROD = "https://lj.du1.net/api"
        private const val BASE_URL_DEV = "https://dev.lj.du1.net/api"

        val BASE_URL = if (AppConfig.isDebugMode()) BASE_URL_DEV else BASE_URL_PROD
        
        // API端点
        const val LOGIN = "/user/login"
        const val REGISTER = "/user/register"
        const val TOKEN_CHECK = "/token/check"
        const val UPLOAD = "/common/upload"
        const val DELETE = "/common/delete"
        const val NEARBY_SITES = "/qyxx/getNearbySites"
        const val ADD_SITE = "/qyxx/add"
        const val VERSION_CHECK = "/index/getLatestAppVersionJson"
    }
    
    /**
     * 网络配置常量
     */
    private object NetworkConfig {
        const val CONNECT_TIMEOUT = 30L
        const val READ_TIMEOUT = 60L
        const val WRITE_TIMEOUT = 60L
        const val CACHE_SIZE = 10 * 1024 * 1024L // 10MB
    }
    
    /**
     * 主HTTP客户端（带缓存）
     */
    fun createHttpClient(context: Context): OkHttpClient {
        val builder = OkHttpClient.Builder()
            .connectTimeout(NetworkConfig.CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(NetworkConfig.READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(NetworkConfig.WRITE_TIMEOUT, TimeUnit.SECONDS)
            .cache(Cache(File(context.cacheDir, "http_cache"), NetworkConfig.CACHE_SIZE))

        // 只在调试模式下添加日志拦截器
        if (AppConfig.isDebugMode()) {
            builder.addInterceptor(createLoggingInterceptor())
        }

        builder.addInterceptor(createAuthInterceptor())
            .addInterceptor(createRetryInterceptor())

        return builder.build()
    }
    
    /**
     * 上传专用HTTP客户端（无缓存，更长超时）
     */
    fun createUploadClient(): OkHttpClient {
        val builder = OkHttpClient.Builder()
            .connectTimeout(60L, TimeUnit.SECONDS)
            .readTimeout(120L, TimeUnit.SECONDS)
            .writeTimeout(120L, TimeUnit.SECONDS)

        // 只在调试模式下添加日志拦截器
        if (AppConfig.isDebugMode()) {
            builder.addInterceptor(createLoggingInterceptor())
        }

        builder.addInterceptor(createAuthInterceptor())
            .addInterceptor(createRetryInterceptor())

        return builder.build()
    }
    
    /**
     * 创建日志拦截器
     */
    private fun createLoggingInterceptor(): HttpLoggingInterceptor {
        return HttpLoggingInterceptor { message ->
            Logger.network(message)
        }.apply {
            level = if (AppConfig.isDebugMode()) {
                HttpLoggingInterceptor.Level.BODY
            } else {
                HttpLoggingInterceptor.Level.BASIC
            }
        }
    }
    
    /**
     * 创建认证拦截器
     */
    private fun createAuthInterceptor(): Interceptor {
        return object : Interceptor {
            override fun intercept(chain: Interceptor.Chain): Response {
                val request = chain.request()
                val versionName = "1.0.0" // 默认版本号

                val newRequest = request.newBuilder()
                    .addHeader("User-Agent", "LJHJ-Android/$versionName")
                    .addHeader("Accept", "application/json")
                    .build()
                return chain.proceed(newRequest)
            }
        }
    }
    
    /**
     * 创建重试拦截器
     */
    private fun createRetryInterceptor(): Interceptor {
        return object : Interceptor {
            override fun intercept(chain: Interceptor.Chain): Response {
                val request = chain.request()
                var response = chain.proceed(request)
                var tryCount = 0
                val maxRetries = 2

                while (!response.isSuccessful && tryCount < maxRetries) {
                    tryCount++
                    Logger.network("Request failed, retry attempt: $tryCount")
                    response.close()
                    response = chain.proceed(request)
                }

                return response
            }
        }
    }
    
    /**
     * 构建完整URL
     */
    fun buildUrl(endpoint: String): String {
        return "${ApiConfig.BASE_URL}$endpoint"
    }
    
    /**
     * 创建带Token的请求构建器
     */
    fun createRequestBuilder(url: String, token: String? = null): Request.Builder {
        val builder = Request.Builder().url(url)
        token?.let { builder.addHeader("Token", it) }
        return builder
    }
}
