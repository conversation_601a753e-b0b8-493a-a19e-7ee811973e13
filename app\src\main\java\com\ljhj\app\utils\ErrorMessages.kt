package com.ljhj.app.utils

/**
 * 统一的错误消息管理
 */
object ErrorMessages {
    
    /**
     * 网络相关错误
     */
    object Network {
        const val CONNECTION_FAILED = "网络连接失败，请检查网络设置"
        const val TIMEOUT = "网络请求超时，请稍后重试"
        const val SERVER_ERROR = "服务器暂时无法响应，请稍后重试"
        const val NO_INTERNET = "无网络连接，请检查网络设置"
        const val UPLOAD_FAILED = "上传失败，图片已保存到本地队列"
        const val DOWNLOAD_FAILED = "下载失败，请检查网络连接"
        
        fun getNetworkError(code: Int): String {
            return when (code) {
                in 400..499 -> "请求错误，请检查输入信息"
                in 500..599 -> "服务器错误，请稍后重试"
                else -> "网络请求失败（错误码：$code）"
            }
        }
    }
    
    /**
     * GPS定位相关错误
     */
    object Location {
        const val PERMISSION_DENIED = "需要位置权限才能使用定位功能"
        const val GPS_DISABLED = "GPS定位服务未开启，请在设置中开启"
        const val GPS_TIMEOUT = "GPS定位超时，请确保在空旷地带并稍后重试"
        const val GPS_ACCURACY_LOW = "GPS信号较弱，正在尝试获取更精确的位置"
        const val GPS_UNAVAILABLE = "GPS服务暂时不可用，请稍后重试"
        const val LOCATION_SERVICES_DISABLED = "位置服务已关闭，请在系统设置中开启"
        
        fun getAccuracyMessage(accuracy: Float): String {
            return when {
                accuracy <= 10 -> "GPS信号优秀（精度：${accuracy.toInt()}米）"
                accuracy <= 50 -> "GPS信号良好（精度：${accuracy.toInt()}米）"
                accuracy <= 100 -> "GPS信号一般（精度：${accuracy.toInt()}米）"
                accuracy <= 300 -> "GPS信号较弱（精度：${accuracy.toInt()}米）"
                else -> "GPS信号很弱（精度：${accuracy.toInt()}米），建议移动到空旷地带"
            }
        }
    }
    
    /**
     * 相机相关错误
     */
    object Camera {
        const val PERMISSION_DENIED = "需要相机权限才能拍照"
        const val CAMERA_UNAVAILABLE = "相机暂时不可用，请稍后重试"
        const val CAPTURE_FAILED = "拍照失败，请重试"
        const val IMAGE_PROCESSING_FAILED = "图片处理失败，请重试"
        const val SAVE_FAILED = "图片保存失败，请检查存储空间"
        const val LOW_STORAGE = "存储空间不足，请清理后重试"
        const val CAMERA_ERROR = "相机出现错误，请重启应用"
    }
    
    /**
     * 存储相关错误
     */
    object Storage {
        const val PERMISSION_DENIED = "需要存储权限才能保存图片"
        const val INSUFFICIENT_SPACE = "存储空间不足，请清理后重试"
        const val WRITE_FAILED = "文件写入失败，请检查存储权限"
        const val READ_FAILED = "文件读取失败，文件可能已损坏"
        const val FILE_NOT_FOUND = "文件不存在或已被删除"
    }
    
    /**
     * 用户认证相关错误
     */
    object Auth {
        const val LOGIN_FAILED = "登录失败，请检查用户名和密码"
        const val TOKEN_EXPIRED = "登录已过期，请重新登录"
        const val UNAUTHORIZED = "没有权限执行此操作"
        const val ACCOUNT_LOCKED = "账户已被锁定，请联系管理员"
        const val INVALID_CREDENTIALS = "用户名或密码错误"
        const val REGISTRATION_FAILED = "注册失败，请稍后重试"
    }
    
    /**
     * 应用相关错误
     */
    object App {
        const val UNKNOWN_ERROR = "发生未知错误，请重试"
        const val FEATURE_UNAVAILABLE = "此功能暂时不可用"
        const val UPDATE_REQUIRED = "需要更新应用才能使用此功能"
        const val MAINTENANCE = "应用正在维护中，请稍后重试"
        const val VERSION_TOO_OLD = "应用版本过旧，请更新到最新版本"
    }
    
    /**
     * 内存相关错误
     */
    object Memory {
        const val LOW_MEMORY = "设备内存不足，请关闭其他应用后重试"
        const val OUT_OF_MEMORY = "内存不足，无法处理图片"
        const val IMAGE_TOO_LARGE = "图片过大，请选择较小的图片"
        const val PROCESSING_FAILED = "图片处理失败，可能是内存不足"
    }
    
    /**
     * 数据相关错误
     */
    object Data {
        const val INVALID_FORMAT = "数据格式错误"
        const val CORRUPTED_DATA = "数据已损坏，请重新获取"
        const val SYNC_FAILED = "数据同步失败，请稍后重试"
        const val BACKUP_FAILED = "数据备份失败"
        const val RESTORE_FAILED = "数据恢复失败"
    }
    
    /**
     * 获取友好的错误提示
     */
    fun getFriendlyMessage(exception: Throwable): String {
        return when (exception) {
            is java.net.UnknownHostException -> Network.NO_INTERNET
            is java.net.SocketTimeoutException -> Network.TIMEOUT
            is java.net.ConnectException -> Network.CONNECTION_FAILED
            is java.io.IOException -> Network.CONNECTION_FAILED
            is SecurityException -> Auth.UNAUTHORIZED
            is OutOfMemoryError -> Memory.OUT_OF_MEMORY
            is IllegalArgumentException -> Data.INVALID_FORMAT
            else -> "${App.UNKNOWN_ERROR}：${exception.message}"
        }
    }
    
    /**
     * 获取操作建议
     */
    fun getActionSuggestion(exception: Throwable): String? {
        return when (exception) {
            is java.net.UnknownHostException -> "请检查网络连接后重试"
            is java.net.SocketTimeoutException -> "请稍后重试或检查网络状况"
            is OutOfMemoryError -> "请关闭其他应用释放内存后重试"
            is SecurityException -> "请检查应用权限设置"
            else -> null
        }
    }
}
