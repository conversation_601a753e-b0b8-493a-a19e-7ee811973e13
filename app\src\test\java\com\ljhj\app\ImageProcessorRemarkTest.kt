package com.ljhj.app

import android.location.Location
import com.ljhj.app.utils.ImageProcessor
import org.junit.Test
import org.junit.Assert.*
import java.lang.reflect.Method

/**
 * 测试ImageProcessor的备注功能
 */
class ImageProcessorRemarkTest {

    /**
     * 测试buildWatermarkLines方法是否正确处理备注
     */
    @Test
    fun buildWatermarkLines_withRemark_shouldIncludeRemarkLine() {
        // 使用反射访问私有方法进行测试
        val method: Method = ImageProcessor::class.java.getDeclaredMethod(
            "buildWatermarkLines",
            String::class.java,
            String::class.java,
            Location::class.java,
            String::class.java
        )
        method.isAccessible = true

        // 创建测试数据
        val companyName = "测试企业"
        val siteName = "测试站点"
        val location = createMockLocation(39.9042, 116.4074)
        val remarkText = "这是一个测试备注"

        // 调用方法
        @Suppress("UNCHECKED_CAST")
        val result = method.invoke(ImageProcessor, companyName, siteName, location, remarkText) as List<String>

        // 验证结果
        assertTrue("应该包含企业名", result.any { it.contains("企业名: $companyName") })
        assertTrue("应该包含站点", result.any { it.contains("站点: $siteName") })
        assertTrue("应该包含GPS信息", result.any { it.contains("GPS经纬度:") })
        assertTrue("应该包含备注", result.any { it.contains("备注: $remarkText") })
        assertTrue("应该包含时间戳", result.any { it.matches(Regex("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) })
    }

    /**
     * 测试buildWatermarkLines方法在没有备注时不包含备注行
     */
    @Test
    fun buildWatermarkLines_withoutRemark_shouldNotIncludeRemarkLine() {
        // 使用反射访问私有方法进行测试
        val method: Method = ImageProcessor::class.java.getDeclaredMethod(
            "buildWatermarkLines",
            String::class.java,
            String::class.java,
            Location::class.java,
            String::class.java
        )
        method.isAccessible = true

        // 创建测试数据（无备注）
        val companyName = "测试企业"
        val siteName = "测试站点"
        val location = createMockLocation(39.9042, 116.4074)
        val remarkText = "" // 空备注

        // 调用方法
        @Suppress("UNCHECKED_CAST")
        val result = method.invoke(ImageProcessor, companyName, siteName, location, remarkText) as List<String>

        // 验证结果
        assertTrue("应该包含企业名", result.any { it.contains("企业名: $companyName") })
        assertTrue("应该包含站点", result.any { it.contains("站点: $siteName") })
        assertTrue("应该包含GPS信息", result.any { it.contains("GPS经纬度:") })
        assertFalse("不应该包含备注行", result.any { it.contains("备注:") })
        assertTrue("应该包含时间戳", result.any { it.matches(Regex("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) })
    }

    /**
     * 测试buildWatermarkLines方法在备注为空白字符时不包含备注行
     */
    @Test
    fun buildWatermarkLines_withBlankRemark_shouldNotIncludeRemarkLine() {
        // 使用反射访问私有方法进行测试
        val method: Method = ImageProcessor::class.java.getDeclaredMethod(
            "buildWatermarkLines",
            String::class.java,
            String::class.java,
            Location::class.java,
            String::class.java
        )
        method.isAccessible = true

        // 创建测试数据（空白备注）
        val companyName = "测试企业"
        val siteName = "测试站点"
        val location = createMockLocation(39.9042, 116.4074)
        val remarkText = "   " // 只有空格的备注

        // 调用方法
        @Suppress("UNCHECKED_CAST")
        val result = method.invoke(ImageProcessor, companyName, siteName, location, remarkText) as List<String>

        // 验证结果
        assertFalse("不应该包含备注行", result.any { it.contains("备注:") })
    }

    /**
     * 测试水印行的顺序是否正确
     */
    @Test
    fun buildWatermarkLines_shouldHaveCorrectOrder() {
        // 使用反射访问私有方法进行测试
        val method: Method = ImageProcessor::class.java.getDeclaredMethod(
            "buildWatermarkLines",
            String::class.java,
            String::class.java,
            Location::class.java,
            String::class.java
        )
        method.isAccessible = true

        // 创建测试数据
        val companyName = "测试企业"
        val siteName = "测试站点"
        val location = createMockLocation(39.9042, 116.4074)
        val remarkText = "测试备注"

        // 调用方法
        @Suppress("UNCHECKED_CAST")
        val result = method.invoke(ImageProcessor, companyName, siteName, location, remarkText) as List<String>

        // 验证顺序：企业名 -> 站点 -> GPS -> 备注 -> 时间戳
        val companyIndex = result.indexOfFirst { it.contains("企业名:") }
        val siteIndex = result.indexOfFirst { it.contains("站点:") }
        val gpsIndex = result.indexOfFirst { it.contains("GPS经纬度:") }
        val remarkIndex = result.indexOfFirst { it.contains("备注:") }
        val timeIndex = result.indexOfFirst { it.matches(Regex("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) }

        assertTrue("企业名应该在站点之前", companyIndex < siteIndex)
        assertTrue("站点应该在GPS之前", siteIndex < gpsIndex)
        assertTrue("GPS应该在备注之前", gpsIndex < remarkIndex)
        assertTrue("备注应该在时间戳之前", remarkIndex < timeIndex)
    }

    /**
     * 创建模拟的Location对象
     */
    private fun createMockLocation(latitude: Double, longitude: Double): Location {
        val location = Location("test")
        location.latitude = latitude
        location.longitude = longitude
        return location
    }
}
