package com.ljhj.app.ui

import android.util.Log
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.datastore.preferences.core.edit
import androidx.navigation.NavController
import com.ljhj.app.R
import com.ljhj.app.TOKEN_KEY
import com.ljhj.app.dataStore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL

@Composable
fun LoginScreen(navController: NavController) {
    var account by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var errorMessage by remember { mutableStateOf("") }
    val coroutineScope = rememberCoroutineScope()

    val gradient = Brush.verticalGradient(
        colors = listOf(Color(0xFF87CEEB), Color(0xFFB0E0E6))
    )

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(brush = gradient)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Top
        ) {
            Spacer(modifier = Modifier.height(10.dp))

            Image(
                painter = painterResource(id = R.drawable.login_city_image),
                contentDescription = "Login Image",
                modifier = Modifier.fillMaxWidth().padding(horizontal = 10.dp)
            )

            Spacer(modifier = Modifier.height(20.dp))

            Text(
                text = "山东绿佳环境技术有限公司",
                style = TextStyle(
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            )

            Spacer(modifier = Modifier.height(30.dp))

            OutlinedTextField(
                value = account,
                onValueChange = { account = it },
                label = { Text("请输入账号", color = Color.Gray) },
                leadingIcon = { Icon(Icons.Default.Person, contentDescription = "Account", tint = Color.Gray) },
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(24.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedContainerColor = Color.White,
                    unfocusedContainerColor = Color.White,
                    disabledContainerColor = Color.White,
                    focusedBorderColor = Color(0xFF87CEEB),
                    unfocusedBorderColor = Color.Transparent,
                    cursorColor = Color(0xFF87CEEB)
                )
            )

            Spacer(modifier = Modifier.height(16.dp))

            OutlinedTextField(
                value = password,
                onValueChange = { password = it },
                label = { Text("请输入密码", color = Color.Gray) },
                leadingIcon = { Icon(Icons.Default.Lock, contentDescription = "Password", tint = Color.Gray) },
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(24.dp),
                visualTransformation = PasswordVisualTransformation(),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedContainerColor = Color.White,
                    unfocusedContainerColor = Color.White,
                    disabledContainerColor = Color.White,
                    focusedBorderColor = Color(0xFF87CEEB),
                    unfocusedBorderColor = Color.Transparent,
                    cursorColor = Color(0xFF87CEEB)
                )
            )

            Spacer(modifier = Modifier.height(32.dp))

            Button(
                onClick = {
                    coroutineScope.launch {
                        val result = login(account, password)
                        if (result.isSuccess) {
                            val token = result.getOrNull()
                            if (token != null) {
                                val context = navController.context
                                coroutineScope.launch {
                                    context.dataStore.edit { preferences ->
                                        preferences[TOKEN_KEY] = token
                                    }
                                    Log.d("LoginToken", "Saved token: $token")
                                }
                                navController.navigate("camera/未设置/未设置") {
                                    popUpTo("login_screen") { inclusive = true }
                                }
                            } else {
                                errorMessage = "登录失败，未获取到Token"
                            }
                        } else {
                            errorMessage = result.exceptionOrNull()?.message ?: "登录失败"
                        }
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(50.dp),
                shape = RoundedCornerShape(24.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF87CEEB)
                )
            ) {
                Text("登 录", fontSize = 18.sp, color = Color.White)
            }

            Spacer(modifier = Modifier.height(16.dp))

            TextButton(onClick = { navController.navigate("register_screen") }) {
                Text("没有账号？立即注册", color = Color.White)
            }

            if (errorMessage.isNotEmpty()) {
                Text(
                    text = errorMessage,
                    color = MaterialTheme.colorScheme.error,
                    modifier = Modifier.padding(top = 8.dp)
                )
            }
        }
    }
}

private suspend fun login(account: String, password: String): Result<String> = withContext(Dispatchers.IO) {
    try {
        val url = URL("https://lj.du1.net/api/user/login")
        val connection = url.openConnection() as HttpURLConnection
        connection.requestMethod = "POST"
        connection.doOutput = true
        connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded")

        val postData = "account=$account&password=$password"

        val outputStreamWriter = OutputStreamWriter(connection.outputStream)
        outputStreamWriter.write(postData)
        outputStreamWriter.flush()

        val responseCode = connection.responseCode
        if (responseCode == HttpURLConnection.HTTP_OK) {
            val reader = BufferedReader(InputStreamReader(connection.inputStream))
            val response = reader.readText()
            val jsonObject = JSONObject(response)
            val code = jsonObject.getInt("code")
            if (code == 1) {
                val data = jsonObject.getJSONObject("data")
                val userinfo = data.getJSONObject("userinfo")
                val token = userinfo.getString("token") // Corrected: token is in data.userinfo.token
                Result.success(token)
            } else {
                val msg = jsonObject.getString("msg")
                Result.failure(Exception(msg))
            }
        } else {
            Result.failure(Exception("登录失败，错误码: $responseCode"))
        }
    } catch (e: Exception) {
        Result.failure(Exception("登录失败: ${e.message}"))
    }
}