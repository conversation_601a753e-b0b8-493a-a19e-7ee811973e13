package com.ljhj.app.utils

import android.graphics.Bitmap
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap

/**
 * 资源管理工具 - 防止内存泄漏
 */
object ResourceManager {
    
    // 弱引用管理Bitmap
    private val bitmapCache = ConcurrentHashMap<String, WeakReference<Bitmap>>()
    
    // 协程作用域管理
    private val coroutineScopes = ConcurrentHashMap<String, CoroutineScope>()
    
    // Job管理
    private val jobs = ConcurrentHashMap<String, Job>()
    
    /**
     * 缓存Bitmap（使用弱引用）
     */
    fun cacheBitmap(key: String, bitmap: Bitmap) {
        bitmapCache[key] = WeakReference(bitmap)
        Logger.d("Bitmap cached with key: $key")
    }
    
    /**
     * 获取缓存的Bitmap
     */
    fun getCachedBitmap(key: String): Bitmap? {
        val weakRef = bitmapCache[key]
        val bitmap = weakRef?.get()
        if (bitmap == null || bitmap.isRecycled) {
            bitmapCache.remove(key)
            return null
        }
        return bitmap
    }
    
    /**
     * 清理指定Bitmap
     */
    fun recycleBitmap(key: String) {
        bitmapCache[key]?.get()?.let { bitmap ->
            if (!bitmap.isRecycled) {
                bitmap.recycle()
                Logger.d("Bitmap recycled: $key")
            }
        }
        bitmapCache.remove(key)
    }
    
    /**
     * 清理所有Bitmap缓存
     */
    fun clearBitmapCache() {
        bitmapCache.values.forEach { weakRef ->
            weakRef.get()?.let { bitmap ->
                if (!bitmap.isRecycled) {
                    bitmap.recycle()
                }
            }
        }
        bitmapCache.clear()
        Logger.d("All bitmap cache cleared")
    }
    
    /**
     * 注册协程作用域
     */
    fun registerCoroutineScope(key: String, scope: CoroutineScope) {
        coroutineScopes[key] = scope
    }
    
    /**
     * 取消协程作用域
     */
    fun cancelCoroutineScope(key: String) {
        coroutineScopes[key]?.cancel()
        coroutineScopes.remove(key)
        Logger.d("Coroutine scope cancelled: $key")
    }
    
    /**
     * 注册Job
     */
    fun registerJob(key: String, job: Job) {
        // 取消之前的Job
        jobs[key]?.cancel()
        jobs[key] = job
    }
    
    /**
     * 取消Job
     */
    fun cancelJob(key: String) {
        jobs[key]?.cancel()
        jobs.remove(key)
        Logger.d("Job cancelled: $key")
    }
    
    /**
     * 清理所有资源
     */
    fun clearAll() {
        clearBitmapCache()
        
        // 取消所有协程
        coroutineScopes.values.forEach { it.cancel() }
        coroutineScopes.clear()
        
        // 取消所有Job
        jobs.values.forEach { it.cancel() }
        jobs.clear()
        
        Logger.d("All resources cleared")
    }
    
    /**
     * 获取内存使用情况
     */
    fun getMemoryInfo(): MemoryInfo {
        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory()
        val totalMemory = runtime.totalMemory()
        val freeMemory = runtime.freeMemory()
        val usedMemory = totalMemory - freeMemory
        
        return MemoryInfo(
            maxMemory = maxMemory,
            totalMemory = totalMemory,
            usedMemory = usedMemory,
            freeMemory = freeMemory,
            availableMemory = maxMemory - usedMemory
        )
    }
    
    data class MemoryInfo(
        val maxMemory: Long,
        val totalMemory: Long,
        val usedMemory: Long,
        val freeMemory: Long,
        val availableMemory: Long
    ) {
        fun getUsagePercentage(): Float = (usedMemory.toFloat() / maxMemory) * 100
        
        fun isMemoryLow(): Boolean = availableMemory < (maxMemory * 0.1) // 少于10%可用内存
    }
}
