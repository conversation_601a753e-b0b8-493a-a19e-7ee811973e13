package com.ljhj.app.ui.state

import androidx.compose.runtime.Stable

/**
 * 通用UI状态管理
 */
@Stable
sealed class UiState<out T> {
    object Idle : UiState<Nothing>()
    object Loading : UiState<Nothing>()
    data class Success<T>(val data: T) : UiState<T>()
    data class Error(
        val exception: Throwable,
        val message: String = exception.message ?: "未知错误",
        val canRetry: Boolean = true,
        val userFriendlyMessage: String = message
    ) : UiState<Nothing>()
}

/**
 * 加载状态
 */
@Stable
data class LoadingState(
    val isLoading: Boolean = false,
    val message: String = "",
    val progress: Float = 0f,
    val showProgress: Boolean = false
) {
    companion object {
        val IDLE = LoadingState()
        fun loading(message: String = "加载中...") = LoadingState(
            isLoading = true,
            message = message
        )
        fun progress(progress: Float, message: String = "处理中...") = LoadingState(
            isLoading = true,
            message = message,
            progress = progress,
            showProgress = true
        )
    }
}

/**
 * 错误状态
 */
@Stable
data class ErrorState(
    val message: String,
    val exception: Throwable? = null,
    val canRetry: Boolean = true,
    val retryAction: (() -> Unit)? = null,
    val dismissAction: (() -> Unit)? = null
) {
    companion object {
        fun network(exception: Throwable, retryAction: (() -> Unit)? = null) = ErrorState(
            message = "网络连接失败，请检查网络设置",
            exception = exception,
            canRetry = true,
            retryAction = retryAction
        )
        
        fun permission(message: String = "需要相关权限才能使用此功能") = ErrorState(
            message = message,
            canRetry = false
        )
        
        fun generic(message: String, exception: Throwable? = null) = ErrorState(
            message = message,
            exception = exception,
            canRetry = false
        )
    }
}

/**
 * 相机状态
 */
@Stable
data class CameraState(
    val isInitialized: Boolean = false,
    val isCapturing: Boolean = false,
    val capturedImages: List<String> = emptyList(),
    val error: ErrorState? = null
)

/**
 * 位置状态
 */
@Stable
data class LocationState(
    val isSearching: Boolean = false,
    val accuracy: Float? = null,
    val address: String = "",
    val latitude: Double = 0.0,
    val longitude: Double = 0.0,
    val error: ErrorState? = null
) {
    val hasLocation: Boolean get() = latitude != 0.0 && longitude != 0.0
    val isAccurate: Boolean get() = accuracy != null && accuracy <= 50f
}

/**
 * 上传状态
 */
@Stable
data class UploadState(
    val isUploading: Boolean = false,
    val progress: Float = 0f,
    val uploadedCount: Int = 0,
    val totalCount: Int = 0,
    val currentFileName: String = "",
    val error: ErrorState? = null
) {
    val progressText: String get() = if (totalCount > 0) {
        "$uploadedCount/$totalCount"
    } else {
        ""
    }
}

/**
 * 权限状态
 */
@Stable
data class PermissionState(
    val hasAllPermissions: Boolean = false,
    val deniedPermissions: List<String> = emptyList(),
    val shouldShowRationale: Boolean = false,
    val permanentlyDenied: Boolean = false
)

/**
 * 网络状态
 */
@Stable
data class NetworkState(
    val isConnected: Boolean = false,
    val connectionType: ConnectionType = ConnectionType.NONE,
    val isMetered: Boolean = false
) {
    enum class ConnectionType {
        NONE, WIFI, MOBILE, ETHERNET
    }
}
