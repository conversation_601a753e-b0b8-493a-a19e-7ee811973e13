# 绿佳环境应用 API 文档

## 概述

绿佳环境应用是一个基于Android平台的环境监测应用，主要用于现场拍照、GPS定位、数据上传等功能。

## 服务器API接口

### 基础信息

- **基础URL**: `https://lj.du1.net/api`
- **认证方式**: Token认证（Header: `Token: {token}`）
- **数据格式**: JSON / Multipart Form Data

### 1. 用户认证

#### 1.1 用户登录

**接口地址**: `POST /user/login`

**请求参数**:
```json
{
    "account": "用户账号",
    "password": "用户密码"
}
```

**响应示例**:
```json
{
    "code": 1,
    "msg": "登录成功",
    "data": {
        "userinfo": {
            "id": 1,
            "username": "用户名",
            "token": "认证令牌"
        }
    }
}
```

#### 1.2 用户注册

**接口地址**: `POST /user/register`

**请求参数**:
```json
{
    "username": "用户名",
    "password": "密码",
    "email": "邮箱地址"
}
```

### 2. 文件上传

#### 2.1 图片上传

**接口地址**: `POST /common/upload`

**请求方式**: `multipart/form-data`

**请求参数**:
- `file`: 图片文件（必需）
- `companyName`: 企业名称（必需）

**请求头**:
- `Token`: 用户认证令牌

**响应示例**:
```json
{
    "code": 1,
    "msg": "上传成功",
    "data": {
        "url": "https://example.com/uploads/image.jpg",
        "filename": "image.jpg",
        "size": 1024000
    }
}
```

#### 2.2 图片删除

**接口地址**: `DELETE /common/delete`

**请求参数**:
```json
{
    "filename": "要删除的文件名",
    "uploadDate": "上传日期 (YYYY-MM-DD)"
}
```

**响应示例**:
```json
{
    "code": 1,
    "msg": "删除成功"
}
```

### 3. 企业信息管理

#### 3.1 获取企业列表

**接口地址**: `GET /qyxx/index`

**响应示例**:
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": [
        {
            "id": 1,
            "EnterpriseName": "企业名称",
            "SubName": "站点名称",
            "Location": "位置信息"
        }
    ]
}
```

#### 3.2 添加企业信息

**接口地址**: `POST /qyxx/add`

**请求参数**:
```json
{
    "EnterpriseName": "企业名称",
    "SubName": "站点名称",
    "Location": "位置信息"
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 请求失败 |
| 1 | 请求成功 |
| -1 | 参数错误 |
| -2 | 认证失败 |
| -3 | 权限不足 |
| -4 | 资源不存在 |
| -5 | 服务器内部错误 |

## 客户端API接口

### 工具类

#### 1. Logger - 日志管理

```kotlin
// 基础日志
Logger.d("调试信息")
Logger.i("普通信息")
Logger.w("警告信息")
Logger.e("错误信息", exception)

// 分类日志
Logger.network("网络请求", url)
Logger.location("位置更新")
Logger.camera("相机操作")
Logger.upload("上传状态")
```

#### 2. ErrorHandler - 错误处理

```kotlin
// 处理网络错误
ErrorHandler.handleNetworkError(context, throwable)

// 处理上传错误
ErrorHandler.handleUploadError(context, throwable, filename)

// 安全执行代码
ErrorHandler.safeExecute(context, "操作名称") {
    // 可能抛出异常的代码
}
```

#### 3. ResourceManager - 资源管理

```kotlin
// Bitmap缓存管理
ResourceManager.cacheBitmap("key", bitmap)
ResourceManager.getCachedBitmap("key")
ResourceManager.recycleBitmap("key")

// 协程管理
ResourceManager.registerCoroutineScope("key", scope)
ResourceManager.cancelCoroutineScope("key")

// 内存监控
val memoryInfo = ResourceManager.getMemoryInfo()
```

#### 4. PermissionManager - 权限管理

```kotlin
// 检查权限
val hasPermissions = PermissionManager.hasAllRequiredPermissions(context)

// 获取被拒绝的权限
val deniedPermissions = PermissionManager.getDeniedPermissions(context)

// 打开应用设置
PermissionManager.openAppSettings(context)
```

### UI组件

#### 1. 状态管理

```kotlin
// UI状态
sealed class UiState<out T> {
    object Idle : UiState<Nothing>()
    object Loading : UiState<Nothing>()
    data class Success<T>(val data: T) : UiState<T>()
    data class Error(val exception: Throwable) : UiState<Nothing>()
}

// 加载状态
data class LoadingState(
    val isLoading: Boolean = false,
    val message: String = "",
    val progress: Float = 0f
)
```

#### 2. 错误处理组件

```kotlin
@Composable
fun EnhancedErrorMessage(
    errorState: ErrorState,
    onRetry: (() -> Unit)? = null,
    onDismiss: (() -> Unit)? = null
)

@Composable
fun EnhancedLoadingIndicator(
    loadingState: LoadingState
)
```

## 数据库设计

### 上传任务表 (upload_tasks)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 主键，自增 |
| photoUri | String | 图片URI |
| workRequestId | UUID | 工作请求ID |
| originalFilename | String | 原始文件名 |
| companyName | String | 企业名称 |
| status | String | 状态 (PENDING/UPLOADING/SUCCESS/FAILED) |
| timestamp | Long | 时间戳 |

## 使用示例

### 拍照上传流程

```kotlin
// 1. 检查权限
if (!PermissionManager.hasAllRequiredPermissions(context)) {
    // 请求权限
    return
}

// 2. 拍照处理
val bitmap = ImageProcessor.processImage(
    imageProxy = imageProxy,
    location = location,
    companyName = companyName,
    siteName = siteName,
    addWatermark = true
)

// 3. 保存图片
val imageUri = saveImageToGallery(context, bitmap, filename)

// 4. 调度上传任务
scheduleUpload(context, imageUri, filename, companyName)
```
