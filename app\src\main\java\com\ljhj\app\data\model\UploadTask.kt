package com.ljhj.app.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.UUID

@Entity(tableName = "upload_tasks")
data class UploadTask(
    @PrimaryKey(autoGenerate = true) val id: Long = 0,
    val photoUri: String,
    val workRequestId: UUID,
    val originalFilename: String,
    val companyName: String,
    var status: String,
    val timestamp: Long = System.currentTimeMillis(),
    val uploadDate: String = "",
    val serverUrl: String? = null // 服务器返回的图片URL
) {
    // 检查是否为今天拍摄的照片
    fun isToday(): Boolean {
        val today = java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault()).format(java.util.Date())
        return uploadDate == today
    }

    // 检查是否可以删除（只能删除今天的照片）
    fun canDelete(): Boolean = isToday()

    companion object {
        /**
         * 创建新的上传任务
         */
        fun create(
            photoUri: String,
            workRequestId: UUID,
            originalFilename: String,
            companyName: String,
            status: String,
            timestamp: Long = System.currentTimeMillis()
        ): UploadTask {
            val uploadDate = java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault())
                .format(java.util.Date(timestamp))

            return UploadTask(
                photoUri = photoUri,
                workRequestId = workRequestId,
                originalFilename = originalFilename,
                companyName = companyName,
                status = status,
                timestamp = timestamp,
                uploadDate = uploadDate
            )
        }
    }
}
